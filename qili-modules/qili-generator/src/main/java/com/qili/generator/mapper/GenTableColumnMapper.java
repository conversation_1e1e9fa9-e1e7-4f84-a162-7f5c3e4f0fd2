package com.qili.generator.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.qili.common.mybatis.core.mapper.BaseMapperPlus;
import com.qili.generator.domain.GenTableColumn;

/**
 * 业务字段 数据层
 *
 * <AUTHOR> Li
 */
@InterceptorIgnore(dataPermission = "true", tenantLine = "true")
public interface GenTableColumnMapper extends BaseMapperPlus<GenTableColumn, GenTableColumn> {

}
