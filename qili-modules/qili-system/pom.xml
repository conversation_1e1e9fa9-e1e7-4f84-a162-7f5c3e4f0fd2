<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.qili</groupId>
        <artifactId>qili-modules</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>qili-system</artifactId>

    <description>
        system系统模块
    </description>

    <dependencies>
        <!-- 通用工具-->
        <dependency>
            <groupId>com.qili</groupId>
            <artifactId>qili-common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qili</groupId>
            <artifactId>qili-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qili</groupId>
            <artifactId>qili-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qili</groupId>
            <artifactId>qili-common-translation</artifactId>
        </dependency>

        <!-- OSS功能模块 -->
        <dependency>
            <groupId>com.qili</groupId>
            <artifactId>qili-common-oss</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qili</groupId>
            <artifactId>qili-common-log</artifactId>
        </dependency>

        <!-- excel-->
        <dependency>
            <groupId>com.qili</groupId>
            <artifactId>qili-common-excel</artifactId>
        </dependency>

        <!-- SMS功能模块 -->
        <dependency>
            <groupId>com.qili</groupId>
            <artifactId>qili-common-sms</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qili</groupId>
            <artifactId>qili-common-tenant</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qili</groupId>
            <artifactId>qili-common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qili</groupId>
            <artifactId>qili-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qili</groupId>
            <artifactId>qili-common-idempotent</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qili</groupId>
            <artifactId>qili-common-sensitive</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qili</groupId>
            <artifactId>qili-common-encrypt</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qili</groupId>
            <artifactId>qili-common-websocket</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qili</groupId>
            <artifactId>qili-common-sse</artifactId>
        </dependency>

    </dependencies>

</project>
