<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.qili</groupId>
        <artifactId>qili-modules</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>qili-clinic</artifactId>
    <description>诊所业务相关模块</description>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- 通用工具-->
        <dependency>
            <groupId>com.qili</groupId>
            <artifactId>qili-common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qili</groupId>
            <artifactId>qili-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qili</groupId>
            <artifactId>qili-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qili</groupId>
            <artifactId>qili-common-translation</artifactId>
        </dependency>
        <!-- excel-->
        <dependency>
            <groupId>com.qili</groupId>
            <artifactId>qili-common-excel</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qili</groupId>
            <artifactId>qili-common-log</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qili</groupId>
            <artifactId>qili-common-tenant</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qili</groupId>
            <artifactId>qili-common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qili</groupId>
            <artifactId>qili-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qili</groupId>
            <artifactId>qili-common-idempotent</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qili</groupId>
            <artifactId>qili-common-sensitive</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qili</groupId>
            <artifactId>qili-common-encrypt</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qili</groupId>
            <artifactId>qili-common-websocket</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qili</groupId>
            <artifactId>qili-common-sse</artifactId>
        </dependency>

    </dependencies>

</project>
