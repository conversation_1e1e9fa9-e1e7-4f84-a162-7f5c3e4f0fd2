import type { PageQuery, BaseEntity } from '#/api/common';

export interface TreatmentProjectVO {
  /**
   * 主键ID
   */
  id: string | number;

  /**
   * 诊疗项目名称
   */
  name: string;

  /**
   * 诊疗项目编码
   */
  code: string;

  /**
   * 项目分类
   */
  category: string;

  /**
   * 项目说明
   */
  description: string;

  /**
   * 单价
   */
  price: number;

  /**
   * 单位
   */
  unit: string;

  /**
   * 预计时长
   */
  durationMinutes: number;

  /**
   * 状态（0=启用，1=停用）
   */
  status: string;

}

export interface TreatmentProjectForm extends BaseEntity {
  /**
   * 主键ID
   */
  id?: string | number;

  /**
   * 诊疗项目名称
   */
  name?: string;

  /**
   * 诊疗项目编码
   */
  code?: string;

  /**
   * 项目分类
   */
  category?: string;

  /**
   * 项目说明
   */
  description?: string;

  /**
   * 单价
   */
  price?: number;

  /**
   * 单位
   */
  unit?: string;

  /**
   * 预计时长
   */
  durationMinutes?: number;

  /**
   * 状态（0=启用，1=停用）
   */
  status?: string;

}

export interface TreatmentProjectQuery extends PageQuery {
  /**
   * 诊疗项目名称
   */
  name?: string;

  /**
   * 诊疗项目编码
   */
  code?: string;

  /**
   * 项目分类
   */
  category?: string;

  /**
   * 状态（0=启用，1=停用）
   */
  status?: string;

  /**
    * 日期范围参数
    */
  params?: any;
}
