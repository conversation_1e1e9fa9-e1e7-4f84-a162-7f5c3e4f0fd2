import type { TreatmentProjectVO, TreatmentProjectForm, TreatmentProjectQuery } from './model';

import type { ID, IDS } from '#/api/common';
import type { PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
* 查询诊疗项目列表
* @param params
* @returns 诊疗项目列表
*/
export function treatmentProjectList(params?: TreatmentProjectQuery) {
  return requestClient.get<PageResult<TreatmentProjectVO>>('/clinic/treatmentProject/list', { params });
}

/**
 * 导出诊疗项目列表
 * @param params
 * @returns 诊疗项目列表
 */
export function treatmentProjectExport(params?: TreatmentProjectQuery) {
  return commonExport('/clinic/treatmentProject/export', params ?? {});
}

/**
 * 查询诊疗项目详情
 * @param id id
 * @returns 诊疗项目详情
 */
export function treatmentProjectInfo(id: ID) {
  return requestClient.get<TreatmentProjectVO>(`/clinic/treatmentProject/${id}`);
}

/**
 * 新增诊疗项目
 * @param data
 * @returns void
 */
export function treatmentProjectAdd(data: TreatmentProjectForm) {
  return requestClient.postWithMsg<void>('/clinic/treatmentProject', data);
}

/**
 * 更新诊疗项目
 * @param data
 * @returns void
 */
export function treatmentProjectUpdate(data: TreatmentProjectForm) {
  return requestClient.putWithMsg<void>('/clinic/treatmentProject', data);
}

/**
 * 删除诊疗项目
 * @param id id
 * @returns void
 */
export function treatmentProjectRemove(id: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/clinic/treatmentProject/${id}`);
}
