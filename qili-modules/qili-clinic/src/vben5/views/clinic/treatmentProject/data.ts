import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'name',
    label: '诊疗项目名称',
  },
  {
    component: 'Input',
    fieldName: 'code',
    label: '诊疗项目编码',
  },
  {
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.QILI_TREATMENT_CATEGORY 便于维护
      options: getDictOptions('qili_treatment_category'),
    },
    fieldName: 'category',
    label: '项目分类',
  },
  {
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.COMMON_YES_NO 便于维护
      options: getDictOptions('common_yes_no'),
    },
    fieldName: 'status',
    label: '状态',
  },
];

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '主键ID',
    field: 'id',
  },
  {
    title: '诊疗项目名称',
    field: 'name',
  },
  {
    title: '诊疗项目编码',
    field: 'code',
  },
  {
    title: '项目分类',
    field: 'category',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.QILI_TREATMENT_CATEGORY 便于维护
        return renderDict(row.category, 'qili_treatment_category');
      },
    },
  },
  {
    title: '项目说明',
    field: 'description',
  },
  {
    title: '单价',
    field: 'price',
  },
  {
    title: '单位',
    field: 'unit',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.QILI_TREATMENT_UNIT 便于维护
        return renderDict(row.unit, 'qili_treatment_unit');
      },
    },
  },
  {
    title: '预计时长',
    field: 'durationMinutes',
  },
  {
    title: '状态',
    field: 'status',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.COMMON_YES_NO 便于维护
        return renderDict(row.status, 'common_yes_no');
      },
    },
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

