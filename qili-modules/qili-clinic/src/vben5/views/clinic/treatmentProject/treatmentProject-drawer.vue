<!--
使用antd原生Form生成 详细用法参考ant-design-vue Form组件文档
vscode默认配置文件会自动格式化/移除未使用依赖
-->
<script setup lang="ts">
import type { RuleObject } from 'ant-design-vue/es/form';
import { computed, ref } from 'vue';

import { Input, Textarea, Select, RadioGroup, CheckboxGroup, DatePicker, Form, FormItem } from 'ant-design-vue';
import { ImageUpload, FileUpload } from '#/components/upload';
import { Tinymce } from '#/components/tinymce';
import { getPopupContainer } from '@vben/utils';
import { pick } from 'lodash-es';

import { getDictOptions } from '#/utils/dict';

import { useVbenDrawer } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { cloneDeep } from '@vben/utils';

import { useVbenForm } from '#/adapter/form';
import { treatmentProjectAdd, treatmentProjectInfo, treatmentProjectUpdate } from '#/api/clinic/treatmentProject';
import type { TreatmentProjectForm } from '#/api/clinic/treatmentProject/model';
import { useBeforeCloseDiff } from '#/utils/popup';

const emit = defineEmits<{ reload: [] }>();

const isUpdate = ref(false);
const title = computed(() => {
  return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
});

/**
 * 定义默认值 用于reset
 */
const defaultValues: Partial<TreatmentProjectForm> = {
  id: undefined,
  name: undefined,
  code: undefined,
  category: undefined,
  description: undefined,
  price: undefined,
  unit: undefined,
  durationMinutes: undefined,
  status: undefined,
}

/**
 * 表单数据ref
 */
const formData = ref(defaultValues);

type AntdFormRules<T> = Partial<Record<keyof T, RuleObject[]>> & {
  [key: string]: RuleObject[];
};
/**
 * 表单校验规则
 */
const formRules = ref<AntdFormRules<TreatmentProjectForm>>({
    category: [
      { required: true, message: "项目分类不能为空" }
    ],
    price: [
      { required: true, message: "单价不能为空" }
    ],
    unit: [
      { required: true, message: "单位不能为空" }
    ],
    status: [
      { required: true, message: "状态不能为空" }
    ],
});

/**
 * useForm解构出表单方法
 */
const { validate, validateInfos, resetFields } = Form.useForm(
  formData,
  formRules,
);

function customFormValueGetter() {
  return JSON.stringify(formData.value);
}

const { onBeforeClose, markInitialized, resetInitialized } = useBeforeCloseDiff(
  {
    initializedGetter: customFormValueGetter,
    currentGetter: customFormValueGetter,
  },
);

const [BasicDrawer, drawerApi] = useVbenDrawer({
  class: 'w-[550px]',
  fullscreenButton: false,
  onBeforeClose,
  onClosed: handleClosed,
  onConfirm: handleConfirm,
  onOpenChange: async (isOpen) => {
    if (!isOpen) {
      return null;
    }
    drawerApi.drawerLoading(true);

    const { id } = drawerApi.getData() as { id?: number | string };
    isUpdate.value = !!id;

    if (isUpdate.value && id) {
      const record = await treatmentProjectInfo(id);
      // 只赋值存在的字段
      const filterRecord = pick(record, Object.keys(defaultValues));
      formData.value = filterRecord;
    }
    await markInitialized();

    drawerApi.drawerLoading(false);
  },
});

async function handleConfirm() {
  try {
    drawerApi.lock(true);
    await validate();
    // 可能会做数据处理 使用cloneDeep深拷贝
    const data = cloneDeep(formData.value);
    await (isUpdate.value ? treatmentProjectUpdate(data) : treatmentProjectAdd(data));
    resetInitialized();
    emit('reload');
    drawerApi.close();
  } catch (error) {
    console.error(error);
  } finally {
    drawerApi.lock(false);
  }
}

async function handleClosed() {
  formData.value = defaultValues;
  resetFields();
  resetInitialized();
}
</script>

<template>
  <BasicDrawer :title="title">
    <Form :label-col="{ span: 4 }">
      <FormItem label="诊疗项目名称" v-bind="validateInfos.name">
        <Input v-model:value="formData.name" :placeholder="$t('ui.formRules.required')" />
      </FormItem>
      <FormItem label="诊疗项目编码" v-bind="validateInfos.code">
        <Input v-model:value="formData.code" :placeholder="$t('ui.formRules.required')" />
      </FormItem>
      <FormItem label="项目分类" v-bind="validateInfos.category">
        <Select
          v-model:value="formData.category"
          :options="getDictOptions('qili_treatment_category')"
          :getPopupContainer="getPopupContainer"
          :placeholder="$t('ui.formRules.selectRequired')"
        />
      </FormItem>
      <FormItem label="项目说明" v-bind="validateInfos.description">
        <Textarea 
          v-model:value="formData.description" 
          :placeholder="$t('ui.formRules.required')" 
          :rows="4" 
        />
      </FormItem>
      <FormItem label="单价" v-bind="validateInfos.price">
        <Input v-model:value="formData.price" :placeholder="$t('ui.formRules.required')" />
      </FormItem>
      <FormItem label="单位" v-bind="validateInfos.unit">
        <Select
          v-model:value="formData.unit"
          :options="getDictOptions('qili_treatment_unit')"
          :getPopupContainer="getPopupContainer"
          :placeholder="$t('ui.formRules.selectRequired')"
        />
      </FormItem>
      <FormItem label="预计时长" v-bind="validateInfos.durationMinutes">
        <Input v-model:value="formData.durationMinutes" :placeholder="$t('ui.formRules.required')" />
      </FormItem>
      <FormItem label="状态" v-bind="validateInfos.status">
        <Select
          v-model:value="formData.status"
          :options="getDictOptions('common_yes_no')"
          :getPopupContainer="getPopupContainer"
          :placeholder="$t('ui.formRules.selectRequired')"
        />
      </FormItem>
    </Form>
  </BasicDrawer>
</template>

