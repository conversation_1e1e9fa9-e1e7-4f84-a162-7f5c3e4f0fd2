package com.qili.clinic.domain.vo;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.qili.clinic.domain.InventoryStockIn;
import com.qili.common.excel.annotation.ExcelDictFormat;
import com.qili.common.excel.convert.ExcelDictConvert;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.qili.common.translation.annotation.Translation;
import com.qili.common.translation.constant.TransConstant;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

/**
 * 库存入库主视图对象 qili_inventory_stock_in
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = InventoryStockIn.class)
public class InventoryStockInVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 入库单号（自动生成如 RK202507240001）
     */
    @ExcelProperty(value = "入库单号", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "自=动生成如,R=K202507240001")
    private String stockInNo;

    /**
     * 药房ID（qili_drug_pharmacy.id）
     */
    @ExcelProperty(value = "药房ID", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "q=ili_drug_pharmacy.id")
    private Long pharmacyId;

    /**
     * 供应商
     */
    @ExcelProperty(value = "供应商")
    private String supplier;

    /**
     * 入库总金额（分）
     */
    @ExcelProperty(value = "入库总金额", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "分")
    private Long totalAmount;

    /**
     * 入库时间
     */
    @ExcelProperty(value = "入库时间")
    private Date stockInTime;

    /**
     * 创建人ID
     */
    private Long createBy;
    /**
     * 创建人昵称
     */
    @Translation(type = TransConstant.USER_ID_TO_NICKNAME, mapper = "createBy")
    @ExcelProperty(value = "创建人")
    private String createName;

    /**
     * 入库明细
     */
    @ExcelProperty(value = "入库明细")
    private List<InventoryStockInItemVo> stockInItemList;

}
