package com.qili.clinic.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.qili.clinic.domain.Drug;
import com.qili.common.excel.annotation.ExcelDictFormat;
import com.qili.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 药品信息视图对象 qili_drug
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Drug.class)
public class DrugVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID，自增
     */
    @ExcelProperty(value = "主键ID，自增")
    private Long id;

    /**
     * 药品名称
     */
    @ExcelProperty(value = "药品名称")
    private String name;

    /**
     * 别名/拼音码（支持模糊搜索）
     */
    @ExcelProperty(value = "别名/拼音码", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "支=持模糊搜索")
    private String alias;

    /**
     * 药品编码
     */
    @ExcelProperty(value = "药品编码")
    private String code;

    /**
     * 规格（如0.25g*10片）
     */
    @ExcelProperty(value = "规格", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "如=0.25g*10片")
    private String specification;

    /**
     * 单位（g/克/片/袋/丸等）
     */
    @ExcelProperty(value = "单位", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "qili_drug_unit")
    private String unit;

    /**
     * 药品分类（中药/西药/成药/草药）
     */
    @ExcelProperty(value = "药品分类", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "qili_drug_category")
    private String category;

    /**
     * 单价（元）
     */
    @ExcelProperty(value = "单价", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "元=")
    private Long price;

    private String priceYuan;

    /**
     * 当前库存数量
     */
    @ExcelProperty(value = "当前库存数量")
    private Long stock;

    /**
     * 库存单位（克/剂/盒等）
     */
    @ExcelProperty(value = "库存单位", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "qili_drug_unit")
    private String stockUnit;

}
