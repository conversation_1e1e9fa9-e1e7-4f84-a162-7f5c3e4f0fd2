package com.qili.clinic.service;

import java.util.Collection;
import java.util.List;

import com.qili.clinic.domain.bo.InventoryChangeLogBo;
import com.qili.clinic.domain.vo.InventoryChangeLogVo;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;

/**
 * 药品库存变更记录Service接口
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
public interface IInventoryChangeLogService {

    /**
     * 查询药品库存变更记录
     *
     * @param id 主键
     * @return 药品库存变更记录
     */
    InventoryChangeLogVo queryById(Long id);

    /**
     * 分页查询药品库存变更记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 药品库存变更记录分页列表
     */
    TableDataInfo<InventoryChangeLogVo> queryPageList(InventoryChangeLogBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的药品库存变更记录列表
     *
     * @param bo 查询条件
     * @return 药品库存变更记录列表
     */
    List<InventoryChangeLogVo> queryList(InventoryChangeLogBo bo);

    /**
     * 新增药品库存变更记录
     *
     * @param bo 药品库存变更记录
     * @return 是否新增成功
     */
    Boolean insertByBo(InventoryChangeLogBo bo);

    /**
     * 修改药品库存变更记录
     *
     * @param bo 药品库存变更记录
     * @return 是否修改成功
     */
    Boolean updateByBo(InventoryChangeLogBo bo);

    /**
     * 校验并批量删除药品库存变更记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 异步保存药品库存变更记录
     *
     * @param bo 药品库存变更记录
     */
    void saveAsync(List<InventoryChangeLogBo> boList);
}
