package com.qili.clinic.service.impl;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qili.clinic.domain.bo.InventoryChangeLogBo;
import com.qili.clinic.domain.InventoryChangeLog;
import com.qili.clinic.domain.vo.InventoryChangeLogVo;
import com.qili.clinic.mapper.InventoryChangeLogMapper;
import com.qili.clinic.service.IInventoryChangeLogService;
import com.qili.common.core.utils.MapstructUtils;
import com.qili.common.core.utils.StringUtils;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;

import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 药品库存变更记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class InventoryChangeLogServiceImpl implements IInventoryChangeLogService {

    private final InventoryChangeLogMapper baseMapper;

    /**
     * 查询药品库存变更记录
     *
     * @param id 主键
     * @return 药品库存变更记录
     */
    @Override
    public InventoryChangeLogVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询药品库存变更记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 药品库存变更记录分页列表
     */
    @Override
    public TableDataInfo<InventoryChangeLogVo> queryPageList(InventoryChangeLogBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<InventoryChangeLog> lqw = buildQueryWrapper(bo);
        Page<InventoryChangeLogVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的药品库存变更记录列表
     *
     * @param bo 查询条件
     * @return 药品库存变更记录列表
     */
    @Override
    public List<InventoryChangeLogVo> queryList(InventoryChangeLogBo bo) {
        LambdaQueryWrapper<InventoryChangeLog> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<InventoryChangeLog> buildQueryWrapper(InventoryChangeLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<InventoryChangeLog> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(InventoryChangeLog::getId);
        lqw.eq(bo.getPharmacyId() != null, InventoryChangeLog::getPharmacyId, bo.getPharmacyId());
        lqw.eq(bo.getDrugId() != null, InventoryChangeLog::getDrugId, bo.getDrugId());
        lqw.like(StringUtils.isNotBlank(bo.getDrugName()), InventoryChangeLog::getDrugName, bo.getDrugName());
        lqw.eq(StringUtils.isNotBlank(bo.getBatchNo()), InventoryChangeLog::getBatchNo, bo.getBatchNo());
        lqw.eq(StringUtils.isNotBlank(bo.getUnit()), InventoryChangeLog::getUnit, bo.getUnit());
        lqw.eq(bo.getChangeType() != null, InventoryChangeLog::getChangeType, bo.getChangeType());
        lqw.eq(bo.getChangeQty() != null, InventoryChangeLog::getChangeQty, bo.getChangeQty());
        lqw.eq(bo.getBeforeQty() != null, InventoryChangeLog::getBeforeQty, bo.getBeforeQty());
        lqw.eq(bo.getAfterQty() != null, InventoryChangeLog::getAfterQty, bo.getAfterQty());
        lqw.eq(StringUtils.isNotBlank(bo.getSourceType()), InventoryChangeLog::getSourceType, bo.getSourceType());
        lqw.eq(ObjectUtil.isNotNull(bo.getSourceNo()), InventoryChangeLog::getSourceNo, bo.getSourceNo());
        lqw.eq(bo.getChangeTime() != null, InventoryChangeLog::getChangeTime, bo.getChangeTime());
        return lqw;
    }

    /**
     * 新增药品库存变更记录
     *
     * @param bo 药品库存变更记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(InventoryChangeLogBo bo) {
        InventoryChangeLog add = MapstructUtils.convert(bo, InventoryChangeLog.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改药品库存变更记录
     *
     * @param bo 药品库存变更记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(InventoryChangeLogBo bo) {
        InventoryChangeLog update = MapstructUtils.convert(bo, InventoryChangeLog.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(InventoryChangeLog entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除药品库存变更记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    @Async
    public void saveAsync(List<InventoryChangeLogBo> boList) {
        List<InventoryChangeLog> addList = MapstructUtils.convert(boList, InventoryChangeLog.class);
        addList.forEach(add -> {
            add.setChangeTime(new Date());
        });
        baseMapper.insertBatch(addList);
    }
}
