package com.qili.clinic.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qili.clinic.domain.bo.MedicalFieldDataBo;
import com.qili.clinic.domain.MedicalFieldData;
import com.qili.clinic.domain.vo.MedicalFieldDataGroupVo;
import com.qili.clinic.domain.vo.MedicalFieldDataVo;
import com.qili.clinic.mapper.MedicalFieldDataMapper;
import com.qili.clinic.service.IMedicalFieldDataService;
import com.qili.common.core.utils.MapstructUtils;
import com.qili.common.core.utils.StringUtils;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 病历字段值Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MedicalFieldDataServiceImpl implements IMedicalFieldDataService {

    private final MedicalFieldDataMapper baseMapper;

    /**
     * 查询病历字段值
     *
     * @param id 主键
     * @return 病历字段值
     */
    @Override
    public MedicalFieldDataVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询病历字段值列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 病历字段值分页列表
     */
    @Override
    public TableDataInfo<MedicalFieldDataVo> queryPageList(MedicalFieldDataBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MedicalFieldData> lqw = buildQueryWrapper(bo);
        Page<MedicalFieldDataVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的病历字段值列表
     *
     * @param bo 查询条件
     * @return 病历字段值列表
     */
    @Override
    public List<MedicalFieldDataVo> queryList(MedicalFieldDataBo bo) {
        LambdaQueryWrapper<MedicalFieldData> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MedicalFieldData> buildQueryWrapper(MedicalFieldDataBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MedicalFieldData> lqw = Wrappers.lambdaQuery();
        lqw.eq(ObjectUtil.isNotNull(bo.getFieldId()), MedicalFieldData::getFieldId, bo.getFieldId());
        lqw.orderByAsc(MedicalFieldData::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getData()), MedicalFieldData::getData, bo.getData());
        return lqw;
    }

    /**
     * 新增病历字段值
     *
     * @param bo 病历字段值
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MedicalFieldDataBo bo) {
        MedicalFieldData add = MapstructUtils.convert(bo, MedicalFieldData.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改病历字段值
     *
     * @param bo 病历字段值
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MedicalFieldDataBo bo) {
        MedicalFieldData update = MapstructUtils.convert(bo, MedicalFieldData.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MedicalFieldData entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除病历字段值信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public List<MedicalFieldDataGroupVo> queryListByFieldId(Long fieldId) {

        LambdaQueryWrapper<MedicalFieldData> lqw = Wrappers.lambdaQuery();
        lqw.eq(MedicalFieldData::getFieldId, fieldId);
        lqw.orderByAsc(MedicalFieldData::getSortOrder);
        List<MedicalFieldDataVo> list = baseMapper.selectVoList(lqw);
        // 根据groupName分组
        Map<String, List<MedicalFieldDataVo>> groupMap = list.stream()
            .collect(Collectors.groupingBy(MedicalFieldDataVo::getGroupName));
        // 根据groupSortOrder排序
        return groupMap.values().stream()
            .map(dataList -> new MedicalFieldDataGroupVo(dataList.get(0).getGroupName(), dataList.get(0).getGroupSortOrder(), dataList.stream()
                .sorted(Comparator.comparingLong(MedicalFieldDataVo::getSortOrder))
                .collect(Collectors.toList())))
            .sorted(Comparator.comparingInt(MedicalFieldDataGroupVo::groupSortOrder))
            .collect(Collectors.toList());
    }

    @Override
    public List<MedicalFieldDataVo> queryListByFields(Collection<Long> fieldIds) {
        LambdaQueryWrapper<MedicalFieldData> lqw = Wrappers.lambdaQuery();
        lqw.in(MedicalFieldData::getFieldId, fieldIds);
        return baseMapper.selectVoList(lqw);
    }
}
