package com.qili.clinic.service.impl;

import com.qili.common.core.utils.MapstructUtils;
import com.qili.common.core.utils.StringUtils;
import com.qili.common.mybatis.core.page.TableDataInfo;
import com.qili.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.qili.clinic.domain.bo.PaymentDetailBo;
import com.qili.clinic.domain.vo.PaymentDetailVo;
import com.qili.clinic.domain.PaymentDetail;
import com.qili.clinic.mapper.PaymentDetailMapper;
import com.qili.clinic.service.IPaymentDetailService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 支付明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PaymentDetailServiceImpl implements IPaymentDetailService {

    private final PaymentDetailMapper baseMapper;

    /**
     * 查询支付明细
     *
     * @param id 主键
     * @return 支付明细
     */
    @Override
    public PaymentDetailVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询支付明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 支付明细分页列表
     */
    @Override
    public TableDataInfo<PaymentDetailVo> queryPageList(PaymentDetailBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PaymentDetail> lqw = buildQueryWrapper(bo);
        Page<PaymentDetailVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的支付明细列表
     *
     * @param bo 查询条件
     * @return 支付明细列表
     */
    @Override
    public List<PaymentDetailVo> queryList(PaymentDetailBo bo) {
        LambdaQueryWrapper<PaymentDetail> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PaymentDetail> buildQueryWrapper(PaymentDetailBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PaymentDetail> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(PaymentDetail::getId);
        lqw.eq(bo.getPaymentId() != null, PaymentDetail::getPaymentId, bo.getPaymentId());
        lqw.eq(bo.getDrugId() != null, PaymentDetail::getDrugId, bo.getDrugId());
        lqw.like(StringUtils.isNotBlank(bo.getItemName()), PaymentDetail::getItemName, bo.getItemName());
        lqw.eq(StringUtils.isNotBlank(bo.getItemCode()), PaymentDetail::getItemCode, bo.getItemCode());
        lqw.eq(bo.getQuantity() != null, PaymentDetail::getQuantity, bo.getQuantity());
        lqw.eq(bo.getPrice() != null, PaymentDetail::getPrice, bo.getPrice());
        lqw.eq(bo.getAmount() != null, PaymentDetail::getAmount, bo.getAmount());
        return lqw;
    }

    /**
     * 新增支付明细
     *
     * @param bo 支付明细
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(PaymentDetailBo bo) {
        PaymentDetail add = MapstructUtils.convert(bo, PaymentDetail.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改支付明细
     *
     * @param bo 支付明细
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(PaymentDetailBo bo) {
        PaymentDetail update = MapstructUtils.convert(bo, PaymentDetail.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PaymentDetail entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除支付明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
