package com.qili.clinic.domain.bo;

import com.qili.clinic.domain.MedicalTemplate;
import com.qili.common.core.validate.AddGroup;
import com.qili.common.core.validate.EditGroup;
import com.qili.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 病历模板业务对象 qili_medical_template
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MedicalTemplate.class, reverseConvertGenerate = false)
public class MedicalTemplateBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 模板名称（如：内科通用模板）
     */
    @NotBlank(message = "模板名称（如：内科通用模板）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 模板类型（中医/西医/中西医结合）
     */
    @NotBlank(message = "模板类型（中医/西医/中西医结合）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String type;

    /**
     * 所属目录
     */
    @NotBlank(message = "所属目录不能为空", groups = { AddGroup.class, EditGroup.class })
    private String directory;

    /**
     * 模板描述
     */
    private String description;

    /**
     * 模板字段关系列表
     */
    private List<MedicalTemplateFieldRelBo> templateFieldRelList;
}
