package com.qili.clinic.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.qili.common.idempotent.annotation.RepeatSubmit;
import com.qili.common.log.annotation.Log;
import com.qili.common.web.core.BaseController;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.core.domain.R;
import com.qili.common.core.validate.AddGroup;
import com.qili.common.core.validate.EditGroup;
import com.qili.common.log.enums.BusinessType;
import com.qili.common.excel.utils.ExcelUtil;
import com.qili.clinic.domain.vo.PaymentDetailVo;
import com.qili.clinic.domain.bo.PaymentDetailBo;
import com.qili.clinic.service.IPaymentDetailService;
import com.qili.common.mybatis.core.page.TableDataInfo;

/**
 * 支付明细
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/clinic/paymentDetail")
public class PaymentDetailController extends BaseController {

    private final IPaymentDetailService paymentDetailService;

    /**
     * 查询支付明细列表
     */
    @SaCheckPermission("clinic:paymentDetail:list")
    @GetMapping("/list")
    public TableDataInfo<PaymentDetailVo> list(PaymentDetailBo bo, PageQuery pageQuery) {
        return paymentDetailService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出支付明细列表
     */
    @SaCheckPermission("clinic:paymentDetail:export")
    @Log(title = "支付明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PaymentDetailBo bo, HttpServletResponse response) {
        List<PaymentDetailVo> list = paymentDetailService.queryList(bo);
        ExcelUtil.exportExcel(list, "支付明细", PaymentDetailVo.class, response);
    }

    /**
     * 获取支付明细详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("clinic:paymentDetail:query")
    @GetMapping("/{id}")
    public R<PaymentDetailVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(paymentDetailService.queryById(id));
    }

    /**
     * 新增支付明细
     */
    @SaCheckPermission("clinic:paymentDetail:add")
    @Log(title = "支付明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PaymentDetailBo bo) {
        return toAjax(paymentDetailService.insertByBo(bo));
    }

    /**
     * 修改支付明细
     */
    @SaCheckPermission("clinic:paymentDetail:edit")
    @Log(title = "支付明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PaymentDetailBo bo) {
        return toAjax(paymentDetailService.updateByBo(bo));
    }

    /**
     * 删除支付明细
     *
     * @param ids 主键串
     */
    @SaCheckPermission("clinic:paymentDetail:remove")
    @Log(title = "支付明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(paymentDetailService.deleteWithValidByIds(List.of(ids), true));
    }
}
