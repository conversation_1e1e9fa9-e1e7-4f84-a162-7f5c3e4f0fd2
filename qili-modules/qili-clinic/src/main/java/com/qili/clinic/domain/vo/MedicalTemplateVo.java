package com.qili.clinic.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.qili.clinic.domain.MedicalTemplate;
import com.qili.common.excel.annotation.ExcelDictFormat;
import com.qili.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 病历模板视图对象 qili_medical_template
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MedicalTemplate.class)
public class MedicalTemplateVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 模板名称（如：内科通用模板）
     */
    @ExcelProperty(value = "模板名称", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "如=：内科通用模板")
    private String name;

    /**
     * 模板类型（中医/西医/中西医结合）
     */
    @ExcelProperty(value = "模板类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "qili_medical_template_type")
    private String type;

    /**
     * 是否默认模板（0：否，1：是）
     */
    @ExcelProperty(value = "是否默认模板", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=：否，1=：是")
    private String defaultFlag;

    /**
     * 所属目录
     */
    @ExcelProperty(value = "所属目录", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "qili_medical_template_directory")
    private String directory;

    /**
     * 模板描述
     */
    @ExcelProperty(value = "模板描述")
    private String description;

    /**
     * 模板字段列表
     */
    private List<MedicalFieldVo> fieldList;

}
