package com.qili.clinic.domain.vo;

import java.util.Date;

import com.qili.clinic.domain.Payment;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.qili.common.excel.annotation.ExcelDictFormat;
import com.qili.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 支付主视图对象 qili_payment
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Payment.class)
public class PaymentVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 支付单号（如 ZF202508140001）
     */
    @ExcelProperty(value = "支付单号", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "如=,Z=F202508140001")
    private String paymentNo;

    /**
     * 患者ID（关联患者表）
     */
    @ExcelProperty(value = "患者ID", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "关=联患者表")
    private Long patientId;

    /**
     * 业务类型（registration挂号/prescription处方/treatment理疗等）
     */
    @ExcelProperty(value = "业务类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "r=egistration挂号/prescription处方/treatment理疗等")
    private String orderType;

    /**
     * 应付总金额（分）
     */
    @ExcelProperty(value = "应付总金额", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "分=")
    private Long totalAmount;

    /**
     * 实付金额（分）
     */
    @ExcelProperty(value = "实付金额", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "分=")
    private Long paidAmount;

    /**
     * 支付状态（pending待支付/paid已支付/failed支付失败/refunded已退款）
     */
    @ExcelProperty(value = "支付状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "p=ending待支付/paid已支付/failed支付失败/refunded已退款")
    private String status;

    /**
     * 支付方式（cash现金/wechat微信/alipay支付宝/bankcard银行卡等）
     */
    @ExcelProperty(value = "支付方式", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "c=ash现金/wechat微信/alipay支付宝/bankcard银行卡等")
    private String paymentMethod;

    /**
     * 支付时间
     */
    @ExcelProperty(value = "支付时间")
    private Date paymentTime;


}
