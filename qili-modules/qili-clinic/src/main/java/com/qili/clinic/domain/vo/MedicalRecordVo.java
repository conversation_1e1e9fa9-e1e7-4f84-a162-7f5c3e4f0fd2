package com.qili.clinic.domain.vo;

import java.util.Date;

import com.qili.clinic.domain.MedicalRecord;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.qili.common.excel.annotation.ExcelDictFormat;
import com.qili.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * 病历/就诊记录视图对象 qili_medical_record
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MedicalRecord.class)
public class MedicalRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 诊号
     */
    @ExcelProperty(value = "诊号")
    private String visitNo;

    /**
     * 患者姓名
     */
    @ExcelProperty(value = "患者姓名")
    private String patientName;

    /**
     * 医生姓名
     */
    @ExcelProperty(value = "医生姓名")
    private String doctorName;

    /**
     * 诊断快照
     */
    private String diagnosisJson;

    /**
     * 中药饮片处方快照
     */
    private String tcmPrescriptionJson;

    /**
     * 中成药处方快照
     */
    private String tcmpPrescriptionJson;

    /**
     * 西药处方快照
     */
    private String wmPrescriptionJson;

    /**
     * 诊疗项目处方
     */
    private String treatmentItemJson;

    /**
     * 就诊状态
     */
    @ExcelProperty(value = "就诊状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "qili_medical_record_status")
    private String status;

    /**
     * 就诊时间
     */
    @ExcelProperty(value = "就诊时间")
    private Date visitTime;

    /**
     * 就诊步骤
     */
    @ExcelProperty(value = "就诊步骤")
    private List<MedicalRecordLogVo> stepList;

}
