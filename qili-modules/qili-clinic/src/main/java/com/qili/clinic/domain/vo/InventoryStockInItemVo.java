package com.qili.clinic.domain.vo;

import java.io.Serial;
import java.io.Serializable;

import com.qili.clinic.domain.InventoryStockInItem;
import com.qili.common.excel.annotation.ExcelDictFormat;
import com.qili.common.excel.convert.ExcelDictConvert;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

/**
 * 库存入库子视图对象 qili_inventory_stock_in_item
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = InventoryStockInItem.class)
public class InventoryStockInItemVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 入库单ID（关联qili_inventory_stock_in.id）
     */
    @ExcelProperty(value = "入库单ID", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "关=联qili_inventory_stock_in.id")
    private Long stockInId;

    /**
     * 药品ID（qili_drug.id）
     */
    @ExcelProperty(value = "药品ID", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "q=ili_drug.id")
    private Long drugId;

    /**
     * 批次号（如20250724-001）
     */
    @ExcelProperty(value = "批次号", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "如=20250724-001")
    private String batchNo;

    /**
     * 进价（分）
     */
    @ExcelProperty(value = "进价", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "分=")
    private Long purchasePrice;

    /**
     * 入库数量
     */
    @ExcelProperty(value = "入库数量")
    private Long quantity;

    /**
     * 小计金额（分）
     */
    @ExcelProperty(value = "小计金额", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "分=")
    private Long amount;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 药品信息
     */
    @ExcelProperty(value = "药品信息")
    private DrugVo drug;

}
