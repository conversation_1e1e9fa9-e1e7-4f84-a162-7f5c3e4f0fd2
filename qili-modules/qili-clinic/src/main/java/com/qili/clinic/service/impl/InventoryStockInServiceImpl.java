package com.qili.clinic.service.impl;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qili.clinic.domain.bo.InventoryChangeLogBo;
import com.qili.clinic.domain.bo.InventoryStockInBo;
import com.qili.clinic.domain.bo.InventoryStockInItemBo;
import com.qili.clinic.domain.Drug;
import com.qili.clinic.domain.InventoryStockIn;
import com.qili.clinic.domain.InventoryStockInItem;
import com.qili.clinic.domain.vo.InventoryStockInItemVo;
import com.qili.clinic.domain.vo.InventoryStockInVo;
import com.qili.clinic.enums.InventoryChangLogChangeType;
import com.qili.clinic.enums.InventoryChangeLogSourceType;
import com.qili.clinic.enums.StockInStatus;
import com.qili.clinic.mapper.DrugMapper;
import com.qili.clinic.mapper.InventoryStockInItemMapper;
import com.qili.clinic.mapper.InventoryStockInMapper;
import com.qili.clinic.service.IInventoryChangeLogService;
import com.qili.clinic.service.IInventoryStockInService;
import com.qili.common.core.exception.ServiceException;
import com.qili.common.core.utils.MapstructUtils;
import com.qili.common.core.utils.StringUtils;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 库存入库主Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class InventoryStockInServiceImpl implements IInventoryStockInService {

    private final InventoryStockInMapper baseMapper;
    private final InventoryStockInItemServiceImpl stockInItemService;
    private final InventoryStockInItemMapper stockInItemMapper;
    private final DrugMapper drugMapper;
    private final IInventoryChangeLogService inventoryChangeLogService;

    /**
     * 查询库存入库主
     *
     * @param id 主键
     * @return 库存入库主
     */
    @Override
    public InventoryStockInVo queryById(Long id) {
        InventoryStockInVo vo = baseMapper.selectVoById(id);
        populateData(List.of(vo));
        return vo;
    }

    /**
     * 分页查询库存入库主列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 库存入库主分页列表
     */
    @Override
    public TableDataInfo<InventoryStockInVo> queryPageList(InventoryStockInBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<InventoryStockIn> lqw = buildQueryWrapper(bo);
        Page<InventoryStockInVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        populateData(result.getRecords());
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的库存入库主列表
     *
     * @param bo 查询条件
     * @return 库存入库主列表
     */
    @Override
    public List<InventoryStockInVo> queryList(InventoryStockInBo bo) {
        LambdaQueryWrapper<InventoryStockIn> lqw = buildQueryWrapper(bo);
        List<InventoryStockInVo> list = baseMapper.selectVoList(lqw);
        populateData(list);
        return list;
    }

    private LambdaQueryWrapper<InventoryStockIn> buildQueryWrapper(InventoryStockInBo bo) {
        LambdaQueryWrapper<InventoryStockIn> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getStockInNo()), InventoryStockIn::getStockInNo, bo.getStockInNo());
        lqw.likeRight(ObjectUtil.isNotNull(bo.getStockInTime()), InventoryStockIn::getStockInTime, bo.getStockInTime());
        lqw.orderByDesc(InventoryStockIn::getId);
        return lqw;
    }

    /**
     * 新增库存入库主
     *
     * @param bo 库存入库主
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = ServiceException.class)
    public Boolean insertByBo(InventoryStockInBo bo) {
        InventoryStockIn add = MapstructUtils.convert(bo, InventoryStockIn.class);
        if (ObjectUtil.isNull(add)) {
            throw new ServiceException("新增库存入库失败，转换失败");
        }
        if (CollUtil.isEmpty(bo.getStockInItemList())) {
            throw new ServiceException("入库明细不能为空");
        }
        Map<Long, Drug> drugMap = this.getDrugMap(bo.getStockInItemList());
        // 填充数据
        List<InventoryStockInItem> stockInItems = this.buildStockInItems(drugMap, bo.getStockInItemList());
        Long totalAmount = this.calcTotalAmount(stockInItems);
        add.setTotalAmount(totalAmount);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        // 绑定明细与主单ID
        Long stockInId = add.getId();
        stockInItems.forEach(item -> item.setStockInId(stockInId));
        // 保存库存入库详情
        stockInItemMapper.insertBatch(stockInItems);
        this.saveChangeLog(add.getId(), drugMap, stockInItems);
        this.updateDrugStock(drugMap, bo.getStockInItemList());
        return flag;
    }

    /**
     * 修改库存入库主
     *
     * @param bo 库存入库主
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(InventoryStockInBo bo) {
        InventoryStockIn update = MapstructUtils.convert(bo, InventoryStockIn.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(InventoryStockIn entity) {
        // 新增状态
        if (ObjectUtil.isNull(entity.getId())) {
            entity.setStockInNo(generateStockInNo());
            entity.setStockInTime(DateUtil.date());
            entity.setStatus(StockInStatus.APPROVED.name());
        }
    }

    /**
     * 生成入库单号
     *
     * @return 入库单号
     */
    public String generateStockInNo() {
        String dateStr = DateUtil.format(DateUtil.date(), DatePattern.PURE_DATE_PATTERN);
        String prefix = "RK" + dateStr; // RK20250806

        // 只查当天最大一条
        LambdaQueryWrapper<InventoryStockIn> lqw = Wrappers.lambdaQuery(InventoryStockIn.class)
                .select(InventoryStockIn::getStockInNo)
                .likeRight(InventoryStockIn::getStockInNo, prefix)
                .orderByDesc(InventoryStockIn::getStockInNo)
                .last("limit 1");

        InventoryStockIn last = baseMapper.selectOne(lqw);

        int nextSeq = 1;
        if (last != null && StrUtil.isNotBlank(last.getStockInNo())) {
            String lastNo = last.getStockInNo(); // e.g. RK202508060123
            String seqStr = lastNo.substring(prefix.length()); // 0123
            nextSeq = Integer.parseInt(seqStr) + 1;
        }
        if (nextSeq > 9999) {
            throw new IllegalStateException("当日入库单号已达上限");
        }
        return prefix + String.format("%04d", nextSeq);
    }

    /**
     * 校验并批量删除库存入库主信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 根据药品id查询药品信息
     *
     * @param stockInItemList 库存入库详情
     * @return 药品信息
     */
    private Map<Long, Drug> getDrugMap(List<InventoryStockInItemBo> stockInItemList) {
        // 根据药品id查询药品信息
        List<Long> drugIds = stockInItemList.stream()
                .map(InventoryStockInItemBo::getDrugId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();
        if (CollUtil.isEmpty(drugIds)) {
            throw new ServiceException("入库明细中的药品不能为空");
        }
        Map<Long, Drug> drugMap = drugMapper.selectByIds(drugIds).stream()
                .collect(Collectors.toMap(Drug::getId, Function.identity()));
        if (MapUtil.isEmpty(drugMap)) {
            throw new ServiceException("未查询到有效药品信息");
        }
        // 校验是否有缺失的药品ID
        Set<Long> missingIds = drugIds.stream()
                .filter(id -> !drugMap.containsKey(id))
                .collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(missingIds)) {
            throw new ServiceException("存在无效的药品ID: " + missingIds);
        }
        return drugMap;
    }

    /**
     * 构建库存入库详情
     *
     * @param drugMap           药品信息Map
     * @param stockInItemBoList 库存入库详情
     * @return 入库详情列表（不包含入库id）
     */
    private List<InventoryStockInItem> buildStockInItems(Map<Long, Drug> drugMap,
            List<InventoryStockInItemBo> stockInItemBoList) {
        String batchNo = RandomUtil.randomNumbers(6);
        return stockInItemBoList.stream()
                .map(item -> {
                    if (item.getQuantity() == null) {
                        throw new ServiceException("数量不能为空");
                    }
                    if (item.getDrugId() == null) {
                        throw new ServiceException("药品不能为空");
                    }
                    InventoryStockInItem stockInItem = MapstructUtils.convert(item, InventoryStockInItem.class);
                    if (ObjectUtil.isNull(stockInItem)) {
                        throw new ServiceException("转换失败");
                    }
                    // 根据药品id获取药品信息
                    Drug drug = drugMap.get(item.getDrugId());
                    if (drug == null) {
                        throw new ServiceException("药品不存在");
                    }
                    if (ObjectUtil.isNotNull(item.getPurchasePrice())) {
                        // 计算金额 单位分
                        long amount = NumberUtil.mul(item.getPurchasePrice(), 100, item.getQuantity()).longValue();
                        stockInItem.setAmount(amount);
                    } else {
                        stockInItem.setAmount(0L);
                    }
                    stockInItem.setBatchNo(batchNo);
                    stockInItem.setDrugName(drug.getName());
                    return stockInItem;
                }).toList();
    }

    /**
     * 计算入库总金额
     *
     * @param stockInItemList 库存入库详情
     * @return 入库总金额
     */
    private Long calcTotalAmount(List<InventoryStockInItem> stockInItemList) {
        return stockInItemList.stream()
                .map(InventoryStockInItem::getAmount)
                .map(amount -> amount == null ? 0L : amount)
                .reduce(0L, Long::sum);
    }

    /**
     * 更新药品库存
     *
     * @param drugMap         药品信息Map
     * @param stockInItemList 库存入库详情
     */
    private void updateDrugStock(Map<Long, Drug> drugMap, List<InventoryStockInItemBo> stockInItemList) {
        if (CollUtil.isEmpty(stockInItemList)) {
            return;
        }
        List<Drug> newDrugList = stockInItemList.stream().map(item -> {
            Drug drug = drugMap.get(item.getDrugId());
            if (drug == null) {
                throw new ServiceException("药品不存在");
            }
            // 更新药品库存
            drug.setStock(drug.getStock() + item.getQuantity());
            return drug;
        }).toList();
        drugMapper.updateBatchById(newDrugList);
    }

    /**
     * 填充信息
     *
     * @param stockInVoList
     */
    private void populateData(List<InventoryStockInVo> voList) {
        voList.forEach(vo -> {
            InventoryStockInItemBo bo = new InventoryStockInItemBo();
            bo.setStockInId(vo.getId());
            List<InventoryStockInItemVo> stockInItemList = stockInItemService.queryList(bo);
            vo.setStockInItemList(stockInItemList);
        });
    }

    /**
     * 保存库存变更记录
     *
     * @param bo 库存入库主
     */
    private void saveChangeLog(Long stockInId, Map<Long, Drug> drugMap, List<InventoryStockInItem> stockInItemList) {
        List<InventoryChangeLogBo> changeLogBoList = stockInItemList.stream().map(item -> {
            Drug drug = drugMap.get(item.getDrugId());
            if (drug == null) {
                throw new ServiceException("药品不存在");
            }
            InventoryChangeLogBo changeLogBo = new InventoryChangeLogBo();
            changeLogBo.setDrugId(item.getDrugId());
            changeLogBo.setDrugName(item.getDrugName());
            changeLogBo.setBatchNo(item.getBatchNo());
            changeLogBo.setUnit(drug.getUnit());
            changeLogBo.setChangeType(InventoryChangLogChangeType.STOCK_IN.getValue());
            changeLogBo.setChangeQty(item.getQuantity());
            changeLogBo.setBeforeQty(drug.getStock());
            changeLogBo.setAfterQty(drug.getStock() + item.getQuantity());
            changeLogBo.setSourceType(InventoryChangeLogSourceType.STOCK_IN.getValue());
            changeLogBo.setSourceNo(stockInId);
            changeLogBo.setChangeTime(DateUtil.date());
            return changeLogBo;
        }).toList();
        inventoryChangeLogService.saveAsync(changeLogBoList);
    }
}
