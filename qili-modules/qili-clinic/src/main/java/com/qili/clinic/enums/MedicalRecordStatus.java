package com.qili.clinic.enums;

import lombok.Getter;

/**
 * 就诊状态枚举
 * 对应 qili_medical_record.status
 */
@Getter
public enum MedicalRecordStatus {

    WAITING("WAITING", "待接诊"),
    IN_PROGRESS("IN_PROGRESS", "就诊中"),
    PRESCRIPTION_DONE("PRESCRIPTION_DONE", "已开方"),
    DISPENSING("DISPENSING", "取药中"),
    DECOCTING("DECOCTING", "煎药中"),
    COMPLETED("COMPLETED", "已完成"),
    CANCELLED("CANCELLED", "已取消");

    private final String code;
    private final String label;

    MedicalRecordStatus(String code, String label) {
        this.code = code;
        this.label = label;
    }

    /**
     * 根据 code 获取枚举
     */
    public static MedicalRecordStatus fromCode(String code) {
        for (MedicalRecordStatus status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的就诊状态: " + code);
    }

    /**
     * 根据 code 获取中文说明
     */
    public static String getLabelByCode(String code) {
        for (MedicalRecordStatus status : values()) {
            if (status.code.equals(code)) {
                return status.label;
            }
        }
        return "";
    }
}
