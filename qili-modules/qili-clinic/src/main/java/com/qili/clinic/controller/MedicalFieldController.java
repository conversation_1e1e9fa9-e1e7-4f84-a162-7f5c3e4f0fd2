package com.qili.clinic.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.qili.clinic.domain.bo.MedicalFieldBo;
import com.qili.clinic.domain.vo.MedicalFieldVo;
import com.qili.clinic.service.IMedicalFieldService;
import com.qili.common.core.domain.R;
import com.qili.common.core.validate.AddGroup;
import com.qili.common.core.validate.EditGroup;
import com.qili.common.excel.utils.ExcelUtil;
import com.qili.common.idempotent.annotation.RepeatSubmit;
import com.qili.common.log.annotation.Log;
import com.qili.common.log.enums.BusinessType;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;
import com.qili.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 病历字段字典
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/clinic/medicalField")
public class MedicalFieldController extends BaseController {

    private final IMedicalFieldService medicalFieldService;

    /**
     * 查询病历字段字典列表
     */
    @SaCheckPermission("clinic:medicalField:list")
    @GetMapping("/list")
    public TableDataInfo<MedicalFieldVo> list(MedicalFieldBo bo, PageQuery pageQuery) {
        return medicalFieldService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出病历字段字典列表
     */
    @SaCheckPermission("clinic:medicalField:export")
    @Log(title = "病历字段字典", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MedicalFieldBo bo, HttpServletResponse response) {
        List<MedicalFieldVo> list = medicalFieldService.queryList(bo);
        ExcelUtil.exportExcel(list, "病历字段字典", MedicalFieldVo.class, response);
    }

    /**
     * 获取病历字段字典详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("clinic:medicalField:query")
    @GetMapping("/{id}")
    public R<MedicalFieldVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(medicalFieldService.queryById(id));
    }

    /**
     * 新增病历字段字典
     */
    @SaCheckPermission("clinic:medicalField:add")
    @Log(title = "病历字段字典", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MedicalFieldBo bo) {
        return toAjax(medicalFieldService.insertByBo(bo));
    }

    /**
     * 修改病历字段字典
     */
    @SaCheckPermission("clinic:medicalField:edit")
    @Log(title = "病历字段字典", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MedicalFieldBo bo) {
        return toAjax(medicalFieldService.updateByBo(bo));
    }

    /**
     * 删除病历字段字典
     *
     * @param ids 主键串
     */
    @SaCheckPermission("clinic:medicalField:remove")
    @Log(title = "病历字段字典", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(medicalFieldService.deleteWithValidByIds(List.of(ids), true));
    }
}
