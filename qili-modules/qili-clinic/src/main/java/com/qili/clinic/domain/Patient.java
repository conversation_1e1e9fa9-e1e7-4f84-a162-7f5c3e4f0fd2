package com.qili.clinic.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.qili.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 患者档案对象 qili_patients
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("qili_patient")
public class Patient extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID，自增
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 患者姓名
     */
    private String name;

    /**
     * 性别（0未知 1男 2女）
     */
    private String gender;

    /**
     * 年龄-年
     */
    private Long ageYear;

    /**
     * 年龄-月
     */
    private Long ageMonth;

    /**
     * 年龄-日
     */
    private Long ageDay;

    /**
     * 出生日期
     */
    private Date dateOfBirth;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 身份证号/证件号
     */
    private String idCard;

    /**
     * 患者来源（如体检/门诊/网络等）
     */
    private String source;

    /**
     * 婚姻状况（未婚/已婚/离异/丧偶）
     */
    private String maritalStatus;

    /**
     * 体重（kg）
     */
    private Long weight;

    /**
     * 省
     */
    private String addressProvince;

    /**
     * 市
     */
    private String addressCity;

    /**
     * 区/县
     */
    private String addressDistrict;

    /**
     * 详细地址
     */
    private String addressDetail;

    /**
     * 职业
     */
    private String occupation;

    /**
     * 档案号
     */
    private String code;

    /**
     * 工作单位
     */
    private String employer;

    /**
     * 民族
     */
    private String ethnicity;

    /**
     * 备注
     */
    private String remark;

    /**
     * 到店原因
     */
    private String visitReason;

    /**
     * 既往史
     */
    private String pastHistory;

    /**
     * 过敏史
     */
    private String allergyHistory;

    /**
     * 标签（多个标签用逗号分隔）
     */
    private String tags;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;


}
