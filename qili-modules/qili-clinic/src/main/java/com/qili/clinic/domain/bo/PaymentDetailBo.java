package com.qili.clinic.domain.bo;

import com.qili.clinic.domain.PaymentDetail;
import com.qili.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 支付明细业务对象 qili_payment_detail
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PaymentDetail.class, reverseConvertGenerate = false)
public class PaymentDetailBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 支付单ID（关联qili_payment.id）
     */
    private Long paymentId;

    /**
     * 药品ID（关联qili_drug.id，非药品项目可为空）
     */
    private Long drugId;

    /**
     * 收费项目名称（如挂号费、针灸、汤药）
     */
    private String itemName;

    /**
     * 收费项目编码
     */
    private String itemCode;

    /**
     * 数量
     */
    private Long quantity;

    /**
     * 单价（分）
     */
    private Long price;
    /**
     * 单价单位
     */
    private String unit;
    /**
     * 小计金额（分）
     */
    private Long amount;

    /**
     * 备注
     */
    private String remark;


}
