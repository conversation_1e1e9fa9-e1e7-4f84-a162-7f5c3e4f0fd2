package com.qili.clinic.domain.bo;

import com.qili.clinic.domain.InventoryStockInItem;
import com.qili.common.core.validate.AddGroup;
import com.qili.common.core.validate.EditGroup;
import com.qili.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 库存入库子业务对象 qili_inventory_stock_in_item
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = InventoryStockInItem.class, reverseConvertGenerate = false)
public class InventoryStockInItemBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 入库单ID（关联qili_inventory_stock_in.id）
     */
    private Long stockInId;

    /**
     * 药品ID（qili_drug.id）
     */
    private Long drugId;

    /**
     * 批次号（如20250724-001）
     */
    @NotBlank(message = "批次号（如20250724-001）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String batchNo;

    /**
     * 进价（分）
     */
    @NotNull(message = "进价不能为空", groups = { AddGroup.class, EditGroup.class })
    private Double purchasePrice;

    /**
     * 入库数量
     */
    @NotNull(message = "入库数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long quantity;

    /**
     * 小计金额（分）
     */
    @NotNull(message = "小计金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long amount;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;


}
