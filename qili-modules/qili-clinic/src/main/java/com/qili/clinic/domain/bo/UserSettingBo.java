package com.qili.clinic.domain.bo;

import com.qili.clinic.domain.UserSetting;
import com.qili.common.mybatis.core.domain.BaseEntity;
import com.qili.common.core.validate.AddGroup;
import com.qili.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 用户个性化设置业务对象 qili_user_setting
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = UserSetting.class, reverseConvertGenerate = false)
public class UserSettingBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 系统设置（JSON格式）
     */
    private String systemSetting;

    /**
     * 打印设置（JSON格式）
     */
    @NotBlank(message = "打印设置（JSON格式）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String printSetting;


}
