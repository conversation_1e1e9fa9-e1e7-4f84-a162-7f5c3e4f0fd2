package com.qili.clinic.service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.qili.clinic.domain.bo.MedicalFieldBo;
import com.qili.clinic.domain.vo.MedicalFieldVo;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;

/**
 * 病历字段字典Service接口
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
public interface IMedicalFieldService {

    /**
     * 查询病历字段字典
     *
     * @param id 主键
     * @return 病历字段字典
     */
    MedicalFieldVo queryById(Long id);

    /**
     * 根据主键集合查询病历字段字典列表
     *
     * @param ids 主键集合
     * @return 病历字段字典列表
     */
    List<MedicalFieldVo> queryListByIds(Collection<Long> ids);

    /**
     * 分页查询病历字段字典列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 病历字段字典分页列表
     */
    TableDataInfo<MedicalFieldVo> queryPageList(MedicalFieldBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的病历字段字典列表
     *
     * @param bo 查询条件
     * @return 病历字段字典列表
     */
    List<MedicalFieldVo> queryList(MedicalFieldBo bo);

    /**
     * 新增病历字段字典
     *
     * @param bo 病历字段字典
     * @return 是否新增成功
     */
    Boolean insertByBo(MedicalFieldBo bo);

    /**
     * 修改病历字段字典
     *
     * @param bo 病历字段字典
     * @return 是否修改成功
     */
    Boolean updateByBo(MedicalFieldBo bo);

    /**
     * 校验并批量删除病历字段字典信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据模板ID列表查询字段列表
     *
     * @param templateIds 模板ID列表
     * @return 模板ID与字段列表的映射
     */
    Map<Long, List<MedicalFieldVo>> listByTemplateIds(Collection<Long> templateIds);

}
