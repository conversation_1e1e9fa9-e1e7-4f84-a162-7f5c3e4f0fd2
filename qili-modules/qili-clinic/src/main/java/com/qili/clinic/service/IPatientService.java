package com.qili.clinic.service;

import com.qili.clinic.domain.bo.PatientBo;
import com.qili.clinic.domain.vo.PatientVo;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 患者档案Service接口
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
public interface IPatientService {

    /**
     * 查询患者档案
     *
     * @param id 主键
     * @return 患者档案
     */
    PatientVo queryById(Long id);

    /**
     * 分页查询患者档案列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 患者档案分页列表
     */
    TableDataInfo<PatientVo> queryPageList(PatientBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的患者档案列表
     *
     * @param bo 查询条件
     * @return 患者档案列表
     */
    List<PatientVo> queryList(PatientBo bo);

    /**
     * 新增患者档案
     *
     * @param bo 患者档案
     * @return 是否新增成功
     */
    PatientVo insertByBo(PatientBo bo);

    /**
     * 修改患者档案
     *
     * @param bo 患者档案
     * @return 是否修改成功
     */
    Boolean updateByBo(PatientBo bo);

    /**
     * 校验并批量删除患者档案信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
