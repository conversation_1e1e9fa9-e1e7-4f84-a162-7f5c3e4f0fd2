package com.qili.clinic.service;

import java.util.Collection;
import java.util.List;

import com.qili.clinic.domain.bo.PaymentBo;
import com.qili.clinic.domain.MedicalRecord;
import com.qili.clinic.domain.vo.PaymentVo;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;

/**
 * 支付主Service接口
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
public interface IPaymentService {

    /**
     * 查询支付主
     *
     * @param id 主键
     * @return 支付主
     */
    PaymentVo queryById(Long id);

    /**
     * 分页查询支付主列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 支付主分页列表
     */
    TableDataInfo<PaymentVo> queryPageList(PaymentBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的支付主列表
     *
     * @param bo 查询条件
     * @return 支付主列表
     */
    List<PaymentVo> queryList(PaymentBo bo);

    /**
     * 新增支付主
     *
     * @param bo 支付主
     * @return 是否新增成功
     */
    Boolean insertByBo(PaymentBo bo);

    /**
     * 校验并批量删除支付主信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 保存支付
     *
     * @param medicalRecord 就诊记录
     * @param payment       支付
     */
    void savePayment(MedicalRecord medicalRecord, PaymentBo payment);
}
