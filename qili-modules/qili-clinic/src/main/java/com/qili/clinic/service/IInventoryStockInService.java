package com.qili.clinic.service;

import com.qili.clinic.domain.bo.InventoryStockInBo;
import com.qili.clinic.domain.vo.InventoryStockInVo;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 库存入库主Service接口
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface IInventoryStockInService {

    /**
     * 查询库存入库主
     *
     * @param id 主键
     * @return 库存入库主
     */
    InventoryStockInVo queryById(Long id);

    /**
     * 分页查询库存入库主列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 库存入库主分页列表
     */
    TableDataInfo<InventoryStockInVo> queryPageList(InventoryStockInBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的库存入库主列表
     *
     * @param bo 查询条件
     * @return 库存入库主列表
     */
    List<InventoryStockInVo> queryList(InventoryStockInBo bo);

    /**
     * 新增库存入库主
     *
     * @param bo 库存入库主
     * @return 是否新增成功
     */
    Boolean insertByBo(InventoryStockInBo bo);

    /**
     * 修改库存入库主
     *
     * @param bo 库存入库主
     * @return 是否修改成功
     */
    Boolean updateByBo(InventoryStockInBo bo);

    /**
     * 校验并批量删除库存入库主信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
