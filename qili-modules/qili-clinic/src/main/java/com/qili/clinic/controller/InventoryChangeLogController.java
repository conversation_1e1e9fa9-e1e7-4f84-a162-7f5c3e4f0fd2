package com.qili.clinic.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.qili.common.idempotent.annotation.RepeatSubmit;
import com.qili.common.log.annotation.Log;
import com.qili.common.web.core.BaseController;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.core.domain.R;
import com.qili.common.core.validate.AddGroup;
import com.qili.common.core.validate.EditGroup;
import com.qili.common.log.enums.BusinessType;
import com.qili.common.excel.utils.ExcelUtil;
import com.qili.clinic.domain.vo.InventoryChangeLogVo;
import com.qili.clinic.domain.bo.InventoryChangeLogBo;
import com.qili.clinic.service.IInventoryChangeLogService;
import com.qili.common.mybatis.core.page.TableDataInfo;

/**
 * 药品库存变更记录
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/clinic/inventoryChangeLog")
public class InventoryChangeLogController extends BaseController {

    private final IInventoryChangeLogService inventoryChangeLogService;

    /**
     * 查询药品库存变更记录列表
     */
    @SaCheckPermission("clinic:inventoryChangeLog:list")
    @GetMapping("/list")
    public TableDataInfo<InventoryChangeLogVo> list(InventoryChangeLogBo bo, PageQuery pageQuery) {
        return inventoryChangeLogService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出药品库存变更记录列表
     */
    @SaCheckPermission("clinic:inventoryChangeLog:export")
    @Log(title = "药品库存变更记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(InventoryChangeLogBo bo, HttpServletResponse response) {
        List<InventoryChangeLogVo> list = inventoryChangeLogService.queryList(bo);
        ExcelUtil.exportExcel(list, "药品库存变更记录", InventoryChangeLogVo.class, response);
    }

    /**
     * 获取药品库存变更记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("clinic:inventoryChangeLog:query")
    @GetMapping("/{id}")
    public R<InventoryChangeLogVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(inventoryChangeLogService.queryById(id));
    }

    /**
     * 新增药品库存变更记录
     */
    @SaCheckPermission("clinic:inventoryChangeLog:add")
    @Log(title = "药品库存变更记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody InventoryChangeLogBo bo) {
        return toAjax(inventoryChangeLogService.insertByBo(bo));
    }

    /**
     * 修改药品库存变更记录
     */
    @SaCheckPermission("clinic:inventoryChangeLog:edit")
    @Log(title = "药品库存变更记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody InventoryChangeLogBo bo) {
        return toAjax(inventoryChangeLogService.updateByBo(bo));
    }

    /**
     * 删除药品库存变更记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("clinic:inventoryChangeLog:remove")
    @Log(title = "药品库存变更记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(inventoryChangeLogService.deleteWithValidByIds(List.of(ids), true));
    }
}
