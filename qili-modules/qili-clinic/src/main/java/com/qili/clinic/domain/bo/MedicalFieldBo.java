package com.qili.clinic.domain.bo;

import com.qili.clinic.domain.MedicalField;
import com.qili.common.core.validate.AddGroup;
import com.qili.common.core.validate.EditGroup;
import com.qili.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 病历字段字典业务对象 qili_medical_field
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MedicalField.class, reverseConvertGenerate = false)
public class MedicalFieldBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 字段编码（如 chief_complaint）
     */
    @NotBlank(message = "字段编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fieldCode;

    /**
     * 字段名称（如主诉）
     */
    @NotBlank(message = "字段名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fieldName;

    /**
     * 字段类型（text/single_select/multi_select）
     */
    private String fieldType;

    /**
     * 是否系统默认字段（1是 0否）
     */
    private String defaultFlag;

    /**
     * 是否必填
     */
    @NotBlank(message = "是否必填不能为空", groups = { AddGroup.class, EditGroup.class })
    private String required;

    /**
     * 备注
     */
    private String remark;

}
