package com.qili.clinic.service.impl;

import com.qili.common.core.utils.MapstructUtils;
import com.qili.common.core.utils.StringUtils;
import com.qili.common.mybatis.core.page.TableDataInfo;
import com.qili.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.qili.clinic.domain.bo.PharmacyBo;
import com.qili.clinic.domain.vo.PharmacyVo;
import com.qili.clinic.domain.Pharmacy;
import com.qili.clinic.mapper.PharmacyMapper;
import com.qili.clinic.service.IPharmacyService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 药房信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PharmacyServiceImpl implements IPharmacyService {

    private final PharmacyMapper baseMapper;

    /**
     * 查询药房信息
     *
     * @param id 主键
     * @return 药房信息
     */
    @Override
    public PharmacyVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询药房信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 药房信息分页列表
     */
    @Override
    public TableDataInfo<PharmacyVo> queryPageList(PharmacyBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Pharmacy> lqw = buildQueryWrapper(bo);
        Page<PharmacyVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的药房信息列表
     *
     * @param bo 查询条件
     * @return 药房信息列表
     */
    @Override
    public List<PharmacyVo> queryList(PharmacyBo bo) {
        LambdaQueryWrapper<Pharmacy> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Pharmacy> buildQueryWrapper(PharmacyBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Pharmacy> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(Pharmacy::getId);
        lqw.like(StringUtils.isNotBlank(bo.getName()), Pharmacy::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getCode()), Pharmacy::getCode, bo.getCode());
        lqw.eq(StringUtils.isNotBlank(bo.getLocation()), Pharmacy::getLocation, bo.getLocation());
        lqw.eq(StringUtils.isNotBlank(bo.getContactPerson()), Pharmacy::getContactPerson, bo.getContactPerson());
        lqw.eq(StringUtils.isNotBlank(bo.getContactPhone()), Pharmacy::getContactPhone, bo.getContactPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), Pharmacy::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增药房信息
     *
     * @param bo 药房信息
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(PharmacyBo bo) {
        Pharmacy add = MapstructUtils.convert(bo, Pharmacy.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改药房信息
     *
     * @param bo 药房信息
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(PharmacyBo bo) {
        Pharmacy update = MapstructUtils.convert(bo, Pharmacy.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Pharmacy entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除药房信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
