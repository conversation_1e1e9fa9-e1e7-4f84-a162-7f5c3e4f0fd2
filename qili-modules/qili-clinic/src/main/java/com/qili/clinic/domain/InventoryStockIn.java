package com.qili.clinic.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.qili.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 库存入库主对象 qili_inventory_stock_in
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("qili_inventory_stock_in")
public class InventoryStockIn extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 入库单号（自动生成如 RK202507240001）
     */
    private String stockInNo;

    /**
     * 药房ID（qili_drug_pharmacy.id）
     */
    private Long pharmacyId;

    /**
     * 供应商
     */
    private String supplier;

    /**
     * 入库总金额（分）
     */
    private Long totalAmount;

    /**
     * 入库时间
     */
    private Date stockInTime;

    /**
     * 状态（pending待审核/approved已入库）
     */
    private String status;

    /**
     * 备注
     */
    private String remark;


    /**
     * 删除标志（0存在 1删除）
     */
    @TableLogic
    private String delFlag;


}
