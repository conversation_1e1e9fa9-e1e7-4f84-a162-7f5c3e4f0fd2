package com.qili.clinic.domain.vo;

import java.util.Date;

import com.qili.clinic.domain.MedicalRecordLog;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.qili.common.excel.annotation.ExcelDictFormat;
import com.qili.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 病历状态变更日志视图对象 qili_medical_record_log
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MedicalRecordLog.class)
public class MedicalRecordLogVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 病历ID（关联qili_medical_record.id）
     */
    @ExcelProperty(value = "病历ID", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "关=联qili_medical_record.id")
    private Long recordId;

    /**
     * 操作状态（WAITING=待接诊, IN_PROGRESS=就诊中, PRESCRIPTION_DONE=处方已开, DISPENSING=取药中, DECOCTING=煎药中, COMPLETED=已完成, CANCELLED=已取消）
     */
    @ExcelProperty(value = "操作状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "W=AITING=待接诊,,I=N_PROGRESS=就诊中,,P=RESCRIPTION_DONE=处方已开,,D=ISPENSING=取药中,,D=ECOCTING=煎药中,,C=OMPLETED=已完成,,C=ANCELLED=已取消")
    private String action;

    /**
     * 操作名称（冗余中文，如 待接诊、就诊中）
     */
    @ExcelProperty(value = "操作名称", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "冗=余中文，如,待=接诊、就诊中")
    private String actionName;

    /**
     * 操作时间
     */
    @ExcelProperty(value = "操作时间")
    private Date actionTime;


}
