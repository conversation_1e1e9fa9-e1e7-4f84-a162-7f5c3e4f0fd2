package com.qili.clinic.enums;

import lombok.Getter;

@Getter
public enum InventoryChangLogChangeType {
    // 1入库 2出库 3盘盈 4盘亏 5退货 6调拨
    STOCK_IN(1, "入库"),
    STOCK_OUT(2, "出库"),
    STOCK_PROFIT(3, "盘盈"),
    STOCK_LOSS(4, "盘亏"),
    RETURN(5, "退货"),
    ALLOCATE(6, "调拨");

    private final Integer value;
    private final String label;

    InventoryChangLogChangeType(Integer value, String label) {
        this.value = value;
        this.label = label;
    }
}
