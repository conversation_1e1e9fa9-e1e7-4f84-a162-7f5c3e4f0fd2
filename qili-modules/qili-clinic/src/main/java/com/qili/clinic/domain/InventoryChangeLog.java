package com.qili.clinic.domain;

import java.io.Serial;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.qili.common.tenant.core.TenantEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 药品库存变更记录对象 qili_inventory_change_log
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("qili_inventory_change_log")
public class InventoryChangeLog extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 药房ID
     */
    private Long pharmacyId;

    /**
     * 药品ID
     */
    private Long drugId;

    /**
     * 药品名称
     */
    private String drugName;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 单位（如 g/片/盒 等）
     */
    private String unit;

    /**
     * 变更类型（1入库 2出库 3盘盈 4盘亏 5退货 6调拨）
     */
    private Long changeType;

    /**
     * 变更数量（正增负减）
     */
    private Long changeQty;

    /**
     * 变更前数量
     */
    private Long beforeQty;

    /**
     * 变更后数量
     */
    private Long afterQty;

    /**
     * 来源类型（stock_in/stock_out/stocktaking/…）
     */
    private String sourceType;

    /**
     * 来源单号或子表ID
     */
    private Long sourceNo;

    /**
     * 变更时间
     */
    private Date changeTime;

    /**
     * 删除标志（0存在 1删除）
     */
    @TableLogic
    private String delFlag;

}
