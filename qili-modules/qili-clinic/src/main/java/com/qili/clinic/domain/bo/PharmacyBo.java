package com.qili.clinic.domain.bo;

import com.qili.clinic.domain.Pharmacy;
import com.qili.common.mybatis.core.domain.BaseEntity;
import com.qili.common.core.validate.AddGroup;
import com.qili.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 药房信息业务对象 qili_pharmacy
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Pharmacy.class, reverseConvertGenerate = false)
public class PharmacyBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 药房名称
     */
    @NotBlank(message = "药房名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 药房编码
     */
    @NotBlank(message = "药房编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String code;

    /**
     * 所在位置
     */
    private String location;

    /**
     * 负责人
     */
    private String contactPerson;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 状态
     */
    @NotBlank(message = "状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 备注
     */
    private String remark;


}
