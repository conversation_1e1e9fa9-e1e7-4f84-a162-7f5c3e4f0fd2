package com.qili.clinic.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.qili.common.idempotent.annotation.RepeatSubmit;
import com.qili.common.log.annotation.Log;
import com.qili.common.web.core.BaseController;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.core.domain.R;
import com.qili.common.core.validate.AddGroup;
import com.qili.common.core.validate.EditGroup;
import com.qili.common.log.enums.BusinessType;
import com.qili.common.excel.utils.ExcelUtil;
import com.qili.clinic.domain.vo.PaymentVo;
import com.qili.clinic.domain.bo.PaymentBo;
import com.qili.clinic.service.IPaymentService;
import com.qili.common.mybatis.core.page.TableDataInfo;

/**
 * 支付主
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/clinic/payment")
public class PaymentController extends BaseController {

    private final IPaymentService paymentService;

    /**
     * 查询支付主列表
     */
    @SaCheckPermission("clinic:payment:list")
    @GetMapping("/list")
    public TableDataInfo<PaymentVo> list(PaymentBo bo, PageQuery pageQuery) {
        return paymentService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出支付主列表
     */
    @SaCheckPermission("clinic:payment:export")
    @Log(title = "支付主", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PaymentBo bo, HttpServletResponse response) {
        List<PaymentVo> list = paymentService.queryList(bo);
        ExcelUtil.exportExcel(list, "支付主", PaymentVo.class, response);
    }

    /**
     * 获取支付主详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("clinic:payment:query")
    @GetMapping("/{id}")
    public R<PaymentVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(paymentService.queryById(id));
    }

    /**
     * 新增支付主
     */
    @SaCheckPermission("clinic:payment:add")
    @Log(title = "支付主", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PaymentBo bo) {
        return toAjax(paymentService.insertByBo(bo));
    }

    /**
     * 删除支付主
     *
     * @param ids 主键串
     */
    @SaCheckPermission("clinic:payment:remove")
    @Log(title = "支付主", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(paymentService.deleteWithValidByIds(List.of(ids), true));
    }
}
