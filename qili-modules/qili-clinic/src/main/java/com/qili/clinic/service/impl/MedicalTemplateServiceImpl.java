package com.qili.clinic.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qili.clinic.domain.MedicalTemplate;
import com.qili.clinic.domain.MedicalTemplateFieldRel;
import com.qili.clinic.domain.bo.MedicalTemplateBo;
import com.qili.clinic.domain.bo.MedicalTemplateFieldRelBo;
import com.qili.clinic.domain.vo.MedicalFieldVo;
import com.qili.clinic.domain.vo.MedicalTemplateVo;
import com.qili.clinic.mapper.MedicalTemplateFieldRelMapper;
import com.qili.clinic.mapper.MedicalTemplateMapper;
import com.qili.clinic.service.IMedicalFieldService;
import com.qili.clinic.service.IMedicalTemplateService;
import com.qili.common.core.constant.SystemConstants;
import com.qili.common.core.exception.ServiceException;
import com.qili.common.core.utils.MapstructUtils;
import com.qili.common.core.utils.StringUtils;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 病历模板Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MedicalTemplateServiceImpl implements IMedicalTemplateService {

    private final MedicalTemplateMapper baseMapper;
    private final IMedicalFieldService medicalFieldService;
    private final MedicalTemplateFieldRelMapper templateFieldRelMapper;

    /**
     * 查询病历模板
     *
     * @param id 主键
     * @return 病历模板
     */
    @Override
    public MedicalTemplateVo queryById(Long id) {
        MedicalTemplateVo vo = baseMapper.selectVoById(id);
        if (ObjectUtil.isNotNull(vo)) {
            populateFieldList(List.of(vo));
        }
        return vo;
    }

    /**
     * 分页查询病历模板列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 病历模板分页列表
     */
    @Override
    public TableDataInfo<MedicalTemplateVo> queryPageList(MedicalTemplateBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MedicalTemplate> lqw = buildQueryWrapper(bo);
        Page<MedicalTemplateVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        populateFieldList(result.getRecords());
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的病历模板列表
     *
     * @param bo 查询条件
     * @return 病历模板列表
     */
    @Override
    public List<MedicalTemplateVo> queryList(MedicalTemplateBo bo) {
        LambdaQueryWrapper<MedicalTemplate> lqw = buildQueryWrapper(bo);
        List<MedicalTemplateVo> vos = baseMapper.selectVoList(lqw);
        populateFieldList(vos);
        return vos;
    }

    private LambdaQueryWrapper<MedicalTemplate> buildQueryWrapper(MedicalTemplateBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MedicalTemplate> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(MedicalTemplate::getId);
        lqw.like(StringUtils.isNotBlank(bo.getName()), MedicalTemplate::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), MedicalTemplate::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getDirectory()), MedicalTemplate::getDirectory, bo.getDirectory());
        lqw.eq(StringUtils.isNotBlank(bo.getDescription()), MedicalTemplate::getDescription, bo.getDescription());
        return lqw;
    }

    /**
     * 新增病历模板
     *
     * @param bo 病历模板
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = ServiceException.class)
    public Boolean insertByBo(MedicalTemplateBo bo) {
        MedicalTemplate add = MapstructUtils.convert(bo, MedicalTemplate.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return this.saveTemplateFieldRefList(bo);
    }

    /**
     * 修改病历模板
     *
     * @param bo 病历模板
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = ServiceException.class)
    public Boolean updateByBo(MedicalTemplateBo bo) {
        MedicalTemplate update = MapstructUtils.convert(bo, MedicalTemplate.class);
        validEntityBeforeSave(update);
        this.saveTemplateFieldRefList(bo);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MedicalTemplate entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除病历模板信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 获取默认模板
     *
     * @return 默认模板
     */
    @Override
    public MedicalTemplateVo queryDefaultTemplate() {
        LambdaQueryWrapper<MedicalTemplate> lqw = Wrappers.lambdaQuery(MedicalTemplate.class);
        lqw.eq(MedicalTemplate::getDefaultFlag, SystemConstants.YES_STR_FLAG);
        MedicalTemplateVo vo = baseMapper.selectVoOne(lqw);
        if (ObjectUtil.isNull(vo)) {
            throw new ServiceException("未找到默认模板");
        }
        Map<Long, List<MedicalFieldVo>> medicalFieldVoMap = medicalFieldService.listByTemplateIds(List.of(vo.getId()));
        vo.setFieldList(medicalFieldVoMap.getOrDefault(vo.getId(), List.of()));
        return vo;
    }

    /**
     * 填充字段列表
     *
     * @param vos 病历模板列表
     */
    private void populateFieldList(Collection<MedicalTemplateVo> vos) {
        List<Long> templateIds = vos.stream().map(MedicalTemplateVo::getId).toList();
        // 字段列表根据模板ID分组
        Map<Long, List<MedicalFieldVo>> fieldVoMap = medicalFieldService.listByTemplateIds(templateIds);
        // 填充字段列表
        vos.forEach(vo -> vo.setFieldList(fieldVoMap.getOrDefault(vo.getId(), List.of())));
    }

    /**
     * 保存字段关联关系
     *
     * @param bo 病历模板
     * @return 是否保存成功
     */
    public boolean saveTemplateFieldRefList(MedicalTemplateBo bo) {
        if (ObjectUtil.isNull(bo.getId())) {
            throw new ServiceException("模板ID不能为空");
        }
        if (CollectionUtil.isEmpty(bo.getTemplateFieldRelList())) {
            log.info("模板ID为{}的病历模板没有关联字段", bo.getId());
            return true;
        }
        List<MedicalTemplateFieldRelBo> fieldList = bo.getTemplateFieldRelList();
        if (CollectionUtil.isEmpty(fieldList)) {
            return true;
        }
        // 删除旧的关联关系
        LambdaQueryWrapper<MedicalTemplateFieldRel> lqw = Wrappers.lambdaQuery();
        lqw.eq(MedicalTemplateFieldRel::getTemplateId, bo.getId());
        templateFieldRelMapper.delete(lqw);

        // 保存新的关联关系
        List<MedicalTemplateFieldRel> relBos = fieldList.stream()
            .map(field -> {
                MedicalTemplateFieldRel fieldRel = new MedicalTemplateFieldRel();
                fieldRel.setTemplateId(bo.getId());
                fieldRel.setFieldId(field.getFieldId());
                fieldRel.setSortOrder(field.getSortOrder());
                return fieldRel;
            }).toList();
        return templateFieldRelMapper.insertBatch(relBos);
    }
}
