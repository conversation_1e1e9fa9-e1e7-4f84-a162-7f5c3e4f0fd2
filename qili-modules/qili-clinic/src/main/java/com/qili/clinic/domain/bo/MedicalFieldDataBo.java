package com.qili.clinic.domain.bo;

import com.qili.clinic.domain.MedicalFieldData;
import com.qili.common.core.validate.AddGroup;
import com.qili.common.core.validate.EditGroup;
import com.qili.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

/**
 * 病历字段值业务对象 qili_medical_field_data
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MedicalFieldData.class, reverseConvertGenerate = false)
@AllArgsConstructor
@NoArgsConstructor
public class MedicalFieldDataBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 分组名称
     */
    @NotBlank(message = "分组名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String groupName;

    /**
     * 分组顺序
     */
    private int groupSortOrder;

    /**
     * 字段ID（关联qili_medical_field.id）
     */
    private Long fieldId;

    /**
     * 字段值（如 咳嗽）
     */
    private String data;

    /**
     * 显示顺序
     */
    @NotNull(message = "显示顺序不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long sortOrder;

    /**
     * 是否系统默认值（1是 0否）
     */
    private String defaultFlag;

}
