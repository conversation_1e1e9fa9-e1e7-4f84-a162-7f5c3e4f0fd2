package com.qili.clinic.service;

import com.qili.clinic.domain.bo.InventoryStockOutBo;
import com.qili.clinic.domain.vo.InventoryStockOutVo;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 库存出库主Service接口
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface IInventoryStockOutService {

    /**
     * 查询库存出库主
     *
     * @param id 主键
     * @return 库存出库主
     */
    InventoryStockOutVo queryById(Long id);

    /**
     * 分页查询库存出库主列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 库存出库主分页列表
     */
    TableDataInfo<InventoryStockOutVo> queryPageList(InventoryStockOutBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的库存出库主列表
     *
     * @param bo 查询条件
     * @return 库存出库主列表
     */
    List<InventoryStockOutVo> queryList(InventoryStockOutBo bo);

    /**
     * 新增库存出库主
     *
     * @param bo 库存出库主
     * @return 是否新增成功
     */
    Boolean insertByBo(InventoryStockOutBo bo);

    /**
     * 修改库存出库主
     *
     * @param bo 库存出库主
     * @return 是否修改成功
     */
    Boolean updateByBo(InventoryStockOutBo bo);

    /**
     * 校验并批量删除库存出库主信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
