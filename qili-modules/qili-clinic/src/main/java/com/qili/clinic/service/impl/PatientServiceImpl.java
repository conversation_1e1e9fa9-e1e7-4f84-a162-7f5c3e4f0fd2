package com.qili.clinic.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qili.clinic.domain.Patient;
import com.qili.clinic.domain.bo.PatientBo;
import com.qili.clinic.domain.vo.MedicalRecordVo;
import com.qili.clinic.domain.vo.PatientVo;
import com.qili.clinic.mapper.PatientMapper;
import com.qili.clinic.service.IMedicalRecordService;
import com.qili.clinic.service.IPatientService;
import com.qili.common.core.utils.MapstructUtils;
import com.qili.common.core.utils.StringUtils;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 患者档案Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PatientServiceImpl implements IPatientService {

    private final PatientMapper baseMapper;
    private final IMedicalRecordService medicalRecordService;

    /**
     * 查询患者档案
     *
     * @param id 主键
     * @return 患者档案
     */
    @Override
    public PatientVo queryById(Long id) {
        PatientVo vo = baseMapper.selectVoById(id);
        populateVisitStatus(vo);
        return vo;
    }

    /**
     * 分页查询患者档案列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 患者档案分页列表
     */
    @Override
    public TableDataInfo<PatientVo> queryPageList(PatientBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Patient> lqw = buildQueryWrapper(bo);
        Page<PatientVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        result.getRecords().forEach(this::populateVisitStatus);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的患者档案列表
     *
     * @param bo 查询条件
     * @return 患者档案列表
     */
    @Override
    public List<PatientVo> queryList(PatientBo bo) {
        LambdaQueryWrapper<Patient> lqw = buildQueryWrapper(bo);
        List<PatientVo> list = baseMapper.selectVoList(lqw);
        list.forEach(this::populateVisitStatus);
        return list;
    }

    private LambdaQueryWrapper<Patient> buildQueryWrapper(PatientBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Patient> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getCode()), Patient::getCode, bo.getCode());
        lqw.orderByDesc(Patient::getId);
        lqw.like(StringUtils.isNotBlank(bo.getName()), Patient::getName, bo.getName());
        // 就诊日期（yyyy-MM-dd），如果“全部”就不传
        if (StringUtils.isNotBlank(bo.getVisitTime())) {
            // 解析成当天 [00:00, 次日00:00)
            LocalDate day = LocalDate.parse(bo.getVisitTime()); // e.g. "2025-10-11"
            LocalDateTime start = day.atStartOfDay();
            LocalDateTime end = day.plusDays(1).atStartOfDay();
            lqw.apply(
                "EXISTS ( " +
                    "  SELECT 1 FROM qili_medical_record mr " +
                    "  WHERE mr.patient_id = qili_patient.id " +
                    "  AND mr.visit_time >= {0} AND mr.visit_time < {1} " +
                    ")",
                start, end
            );
        }
        return lqw;
    }

    /**
     * 新增患者档案
     *
     * @param bo 患者档案
     * @return 是否新增成功
     */
    @Override
    public PatientVo insertByBo(PatientBo bo) {
        Patient add = MapstructUtils.convert(bo, Patient.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return MapstructUtils.convert(add, PatientVo.class);
    }

    /**
     * 修改患者档案
     *
     * @param bo 患者档案
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(PatientBo bo) {
        Patient update = MapstructUtils.convert(bo, Patient.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Patient entity) {
        //TODO 做一些数据校验,如唯一约束
        //新增情况
        if (ObjectUtil.isNull(entity.getId())) {
            entity.setCode(generateMedicalRecordNo());
        }
    }


    /**
     * 校验并批量删除患者档案信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 填充患者档案的就诊状态
     *
     * @param vo 患者档案
     */
    private void populateVisitStatus(PatientVo vo) {
        MedicalRecordVo latest = medicalRecordService.queryLatestByPatientId(vo.getId());
        if (ObjectUtil.isNull(latest)) {
            return;
        }
        vo.setMedicalRecord(latest);
    }


    /**
     * 生成患者档案号
     *
     * @return 患者档案号
     */
    private String generateMedicalRecordNo() {
        //获取数据库中最大的诊号，进行累加
        LambdaQueryWrapper<Patient> lqw = Wrappers.lambdaQuery();
        lqw.select(Patient::getCode);
        lqw.orderByDesc(Patient::getId);
        lqw.last("limit 1");
        Patient patient = baseMapper.selectOne(lqw);
        if (ObjectUtil.isNull(patient)) {
            return "000001";
        }
        String medicalRecordNo = patient.getCode();
        int i = Integer.parseInt(medicalRecordNo) + 1;
        return String.format("%06d", i);
    }
}
