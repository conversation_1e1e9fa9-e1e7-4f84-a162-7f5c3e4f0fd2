package com.qili.clinic.domain.bo;

import com.qili.clinic.domain.Patient;
import com.qili.common.core.validate.AddGroup;
import com.qili.common.core.validate.EditGroup;
import com.qili.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 患者档案业务对象 qili_patients
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Patient.class, reverseConvertGenerate = false)
public class PatientBo extends BaseEntity {

    /**
     * 主键ID，自增
     */
    private Long id;

    /**
     * 患者姓名
     */
    @NotBlank(message = "患者姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 性别（0未知 1男 2女）
     */
    @NotNull(message = "性别（0未知 1男 2女）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String gender;

    /**
     * 年龄-年
     */
    private Long ageYear;

    /**
     * 年龄-月
     */
    private Long ageMonth;

    /**
     * 年龄-日
     */
    private Long ageDay;

    /**
     * 出生日期
     */
    private Date dateOfBirth;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 身份证号/证件号
     */
    private String idCard;

    /**
     * 患者来源（如体检/门诊/网络等）
     */
    private String source;

    /**
     * 婚姻状况（未婚/已婚/离异/丧偶）
     */
    private String maritalStatus;

    /**
     * 体重（kg）
     */
    private Long weight;

    /**
     * 省
     */
    private String addressProvince;

    /**
     * 市
     */
    private String addressCity;

    /**
     * 区/县
     */
    private String addressDistrict;

    /**
     * 详细地址
     */
    private String addressDetail;

    /**
     * 职业
     */
    private String occupation;

    /**
     * 档案号
     */
    private String code;

    /**
     * 工作单位
     */
    private String employer;

    /**
     * 民族
     */
    private String ethnicity;

    /**
     * 备注
     */
    private String remark;

    /**
     * 到店原因
     */
    private String visitReason;

    /**
     * 既往史
     */
    private String pastHistory;

    /**
     * 过敏史
     */
    private String allergyHistory;

    /**
     * 标签（多个标签用逗号分隔）
     */
    private String tags;

    private String visitTime;

}
