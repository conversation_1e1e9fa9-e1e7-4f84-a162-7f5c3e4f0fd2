package com.qili.clinic.enums;

import lombok.Getter;

@Getter
public enum InventoryChangeLogSourceType {
    STOCK_IN("stock_in", "入库"),
    STOCK_OUT("stock_out", "出库"),
    ADJUST("adjust", "调拨"),
    RETURN("return", "退货"),
    OTHER("other", "其他");

    private final String value;
    private final String label;

    InventoryChangeLogSourceType(String value, String label) {
        this.value = value;
        this.label = label;
    }

}
