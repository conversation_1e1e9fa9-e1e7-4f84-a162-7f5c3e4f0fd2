package com.qili.clinic.domain.bo;

import com.qili.clinic.domain.InventoryStockOutItem;
import com.qili.common.core.validate.AddGroup;
import com.qili.common.core.validate.EditGroup;
import com.qili.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 库存出库子业务对象 qili_inventory_stock_out_item
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = InventoryStockOutItem.class, reverseConvertGenerate = false)
public class InventoryStockOutItemBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 出库单ID（关联qili_inventory_stock_out.id）
     */
    private Long stockOutId;

    /**
     * 药品ID（qili_drug.id）
     */
    private Long drugId;

    /**
     * 出库批次号（可选）
     */
    @NotBlank(message = "出库批次号（可选）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String batchNo;

    /**
     * 出库价格（分）
     */
    @NotNull(message = "出库价格（分）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long sellPrice;

    /**
     * 出库数量
     */
    @NotNull(message = "出库数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long quantity;

    /**
     * 小计金额（分）
     */
    @NotNull(message = "小计金额（分）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long amount;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;


}
