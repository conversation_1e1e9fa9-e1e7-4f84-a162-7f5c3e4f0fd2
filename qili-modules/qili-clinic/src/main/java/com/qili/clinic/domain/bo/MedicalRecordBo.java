package com.qili.clinic.domain.bo;

import java.util.Date;

import com.qili.clinic.domain.MedicalRecord;
import com.qili.common.core.validate.AddGroup;
import com.qili.common.core.validate.EditGroup;
import com.qili.common.mybatis.core.domain.BaseEntity;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 病历/就诊记录业务对象 qili_medical_record
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MedicalRecord.class)
public class MedicalRecordBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 诊号
     */
    private String visitNo;

    /**
     * 患者ID
     */
    @NotNull(message = "患者ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long patientId;

    /**
     * 患者姓名
     */
    @NotBlank(message = "患者姓名不能为空", groups = { AddGroup.class })
    private String patientName;

    /**
     * 医生ID
     */
    private Long doctorId;

    /**
     * 医生姓名
     */
    private String doctorName;

    /**
     * 科室ID
     */
    private Long deptId;

    /**
     * 就诊状态
     */
    @NotBlank(message = "就诊状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 就诊时间
     */
    private Date visitTime;

    /**
     * 诊断快照
     */
    private String diagnosisJson;

    /**
     * 中药饮片处方快照
     */
    private String tcmPrescriptionJson;

    /**
     * 中成药处方快照
     */
    private String tcmpPrescriptionJson;

    /**
     * 西药处方快照
     */
    private String wmPrescriptionJson;

    /**
     * 诊疗项目处方
     */
    private String treatmentItemJson;

    /**
     * 备注
     */
    private String remark;

    /**
     * 支付记录
     */
    private PaymentBo payment;

}
