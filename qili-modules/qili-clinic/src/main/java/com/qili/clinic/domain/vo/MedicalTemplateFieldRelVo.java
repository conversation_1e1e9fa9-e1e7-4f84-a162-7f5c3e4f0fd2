package com.qili.clinic.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.qili.clinic.domain.MedicalTemplateFieldRel;
import com.qili.common.excel.annotation.ExcelDictFormat;
import com.qili.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 病历模板字段视图对象 qili_medical_template_field_rel
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MedicalTemplateFieldRel.class)
public class MedicalTemplateFieldRelVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 模板ID（关联qili_medical_template.id）
     */
    @ExcelProperty(value = "模板ID", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "关=联qili_medical_template.id")
    private Long templateId;

    /**
     * 字段ID（关联qili_medical_field.id）
     */
    @ExcelProperty(value = "字段ID", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "关=联qili_medical_field.id")
    private Long fieldId;

    /**
     * 字段显示顺序
     */
    @ExcelProperty(value = "字段显示顺序")
    private Long sortOrder;


}
