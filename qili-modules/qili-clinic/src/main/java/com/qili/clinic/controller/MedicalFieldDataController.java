package com.qili.clinic.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.ObjectUtil;
import com.qili.clinic.domain.bo.MedicalFieldDataBo;
import com.qili.clinic.domain.vo.MedicalFieldDataGroupVo;
import com.qili.clinic.domain.vo.MedicalFieldDataVo;
import com.qili.clinic.service.IMedicalFieldDataService;
import com.qili.common.core.domain.R;
import com.qili.common.core.exception.ServiceException;
import com.qili.common.core.validate.AddGroup;
import com.qili.common.core.validate.EditGroup;
import com.qili.common.excel.utils.ExcelUtil;
import com.qili.common.idempotent.annotation.RepeatSubmit;
import com.qili.common.log.annotation.Log;
import com.qili.common.log.enums.BusinessType;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;
import com.qili.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 病历字段值
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/clinic/medicalFieldData")
public class MedicalFieldDataController extends BaseController {

    private final IMedicalFieldDataService medicalFieldDataService;

    /**
     * 查询病历字段值列表
     */
    @SaCheckPermission("clinic:medicalFieldData:list")
    @GetMapping("/list")
    public TableDataInfo<MedicalFieldDataVo> list(MedicalFieldDataBo bo, PageQuery pageQuery) {
        return medicalFieldDataService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出病历字段值列表
     */
    @SaCheckPermission("clinic:medicalFieldData:export")
    @Log(title = "病历字段值", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MedicalFieldDataBo bo, HttpServletResponse response) {
        List<MedicalFieldDataVo> list = medicalFieldDataService.queryList(bo);
        ExcelUtil.exportExcel(list, "病历字段值", MedicalFieldDataVo.class, response);
    }

    /**
     * 获取病历字段值详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("clinic:medicalFieldData:query")
    @GetMapping("/{id}")
    public R<MedicalFieldDataVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(medicalFieldDataService.queryById(id));
    }

    /**
     * 新增病历字段值
     */
    @SaCheckPermission("clinic:medicalFieldData:add")
    @Log(title = "病历字段值", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MedicalFieldDataBo bo) {
        return toAjax(medicalFieldDataService.insertByBo(bo));
    }

    /**
     * 修改病历字段值
     */
    @SaCheckPermission("clinic:medicalFieldData:edit")
    @Log(title = "病历字段值", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MedicalFieldDataBo bo) {
        return toAjax(medicalFieldDataService.updateByBo(bo));
    }

    /**
     * 删除病历字段值
     *
     * @param ids 主键串
     */
    @SaCheckPermission("clinic:medicalFieldData:remove")
    @Log(title = "病历字段值", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(medicalFieldDataService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 根据字段ID查询病历字段值列表
     */
    //@SaCheckPermission("clinic:medicalFieldData:query")
    @GetMapping("/list/fieldId/{fieldId}")
    public R<List<MedicalFieldDataGroupVo>> listByFieldId(@PathVariable Long fieldId) {
        if (ObjectUtil.isNull(fieldId)) {
            throw new ServiceException("字段ID不能为空");
        }
        return R.ok(medicalFieldDataService.queryListByFieldId(fieldId));
    }
}
