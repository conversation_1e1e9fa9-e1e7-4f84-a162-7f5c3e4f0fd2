package com.qili.clinic.domain.vo;

import com.qili.clinic.domain.PaymentDetail;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.qili.common.excel.annotation.ExcelDictFormat;
import com.qili.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 支付明细视图对象 qili_payment_detail
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PaymentDetail.class)
public class PaymentDetailVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 支付单ID（关联qili_payment.id）
     */
    @ExcelProperty(value = "支付单ID", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "关=联qili_payment.id")
    private Long paymentId;

    /**
     * 药品ID（关联qili_drug.id，非药品项目可为空）
     */
    @ExcelProperty(value = "药品ID", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "关=联qili_drug.id，非药品项目可为空")
    private Long drugId;

    /**
     * 收费项目名称（如挂号费、针灸、汤药）
     */
    @ExcelProperty(value = "收费项目名称", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "如=挂号费、针灸、汤药")
    private String itemName;

    /**
     * 收费项目编码
     */
    @ExcelProperty(value = "收费项目编码")
    private String itemCode;

    /**
     * 数量
     */
    @ExcelProperty(value = "数量")
    private Long quantity;

    /**
     * 单价（分）
     */
    @ExcelProperty(value = "单价", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "分=")
    private Long price;

    /**
     * 单价单位
     */
    private String unit;

    /**
     * 小计金额（分）
     */
    @ExcelProperty(value = "小计金额", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "分=")
    private Long amount;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
