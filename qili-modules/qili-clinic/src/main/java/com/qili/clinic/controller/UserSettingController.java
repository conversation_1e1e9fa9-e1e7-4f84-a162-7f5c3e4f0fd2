package com.qili.clinic.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.qili.common.idempotent.annotation.RepeatSubmit;
import com.qili.common.log.annotation.Log;
import com.qili.common.web.core.BaseController;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.core.domain.R;
import com.qili.common.core.validate.AddGroup;
import com.qili.common.core.validate.EditGroup;
import com.qili.common.log.enums.BusinessType;
import com.qili.common.excel.utils.ExcelUtil;
import com.qili.clinic.domain.vo.UserSettingVo;
import com.qili.clinic.domain.bo.UserSettingBo;
import com.qili.clinic.service.IUserSettingService;
import com.qili.common.mybatis.core.page.TableDataInfo;

/**
 * 用户个性化设置
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/clinic/userSetting")
public class UserSettingController extends BaseController {

    private final IUserSettingService userSettingService;

    /**
     * 查询用户个性化设置列表
     */
    @SaCheckPermission("clinic:userSetting:list")
    @GetMapping("/list")
    public TableDataInfo<UserSettingVo> list(UserSettingBo bo, PageQuery pageQuery) {
        return userSettingService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出用户个性化设置列表
     */
    @SaCheckPermission("clinic:userSetting:export")
    @Log(title = "用户个性化设置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(UserSettingBo bo, HttpServletResponse response) {
        List<UserSettingVo> list = userSettingService.queryList(bo);
        ExcelUtil.exportExcel(list, "用户个性化设置", UserSettingVo.class, response);
    }

    /**
     * 获取用户个性化设置详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("clinic:userSetting:query")
    @GetMapping("/{id}")
    public R<UserSettingVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(userSettingService.queryById(id));
    }

    /**
     * 新增用户个性化设置
     */
    @SaCheckPermission("clinic:userSetting:add")
    @Log(title = "用户个性化设置", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody UserSettingBo bo) {
        return toAjax(userSettingService.insertByBo(bo));
    }

    /**
     * 修改用户个性化设置
     */
    @SaCheckPermission("clinic:userSetting:edit")
    @Log(title = "用户个性化设置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody UserSettingBo bo) {
        return toAjax(userSettingService.updateByBo(bo));
    }

    /**
     * 删除用户个性化设置
     *
     * @param ids 主键串
     */
    @SaCheckPermission("clinic:userSetting:remove")
    @Log(title = "用户个性化设置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(userSettingService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 获取当前用户的用户个性化设置
     */
    @SaCheckPermission("clinic:userSetting:query")
    @GetMapping("/current")
    public R<UserSettingVo> getByCurrentUser() {
        return R.ok(userSettingService.queryByCurrentUser());
    }
}
