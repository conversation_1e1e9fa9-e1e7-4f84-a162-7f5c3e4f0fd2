package com.qili.clinic.domain;

import com.qili.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 诊疗项目对象 qili_treatment_project
 *
 * <AUTHOR>
 * @date 2025-08-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("qili_treatment_project")
public class TreatmentProject extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 诊疗项目名称
     */
    private String name;

    /**
     * 诊疗项目编码
     */
    private String code;

    /**
     * 项目分类
     */
    private String category;

    /**
     * 项目说明
     */
    private String description;

    /**
     * 单价
     */
    private Long price;

    /**
     * 单位
     */
    private String unit;

    /**
     * 预计时长
     */
    private Long durationMinutes;

    /**
     * 状态（0=启用，1=停用）
     */
    private String status;

    /**
     * 删除标志（0=存在，1=删除）
     */
    @TableLogic
    private String delFlag;


}
