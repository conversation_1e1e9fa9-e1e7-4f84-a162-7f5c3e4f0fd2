package com.qili.clinic.service.impl;

import com.qili.common.core.domain.model.LoginUser;
import com.qili.common.core.exception.ServiceException;
import com.qili.common.core.utils.MapstructUtils;
import com.qili.common.core.utils.StringUtils;
import com.qili.common.mybatis.core.page.TableDataInfo;
import com.qili.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.qili.common.satoken.utils.LoginHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.qili.clinic.domain.bo.UserSettingBo;
import com.qili.clinic.domain.vo.UserSettingVo;
import com.qili.clinic.domain.UserSetting;
import com.qili.clinic.mapper.UserSettingMapper;
import com.qili.clinic.service.IUserSettingService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 用户个性化设置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class UserSettingServiceImpl implements IUserSettingService {

    private final UserSettingMapper baseMapper;

    /**
     * 查询用户个性化设置
     *
     * @param id 主键
     * @return 用户个性化设置
     */
    @Override
    public UserSettingVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询用户个性化设置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 用户个性化设置分页列表
     */
    @Override
    public TableDataInfo<UserSettingVo> queryPageList(UserSettingBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<UserSetting> lqw = buildQueryWrapper(bo);
        Page<UserSettingVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的用户个性化设置列表
     *
     * @param bo 查询条件
     * @return 用户个性化设置列表
     */
    @Override
    public List<UserSettingVo> queryList(UserSettingBo bo) {
        LambdaQueryWrapper<UserSetting> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<UserSetting> buildQueryWrapper(UserSettingBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<UserSetting> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(UserSetting::getId);
        lqw.eq(bo.getUserId() != null, UserSetting::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getSystemSetting()), UserSetting::getSystemSetting, bo.getSystemSetting());
        lqw.eq(StringUtils.isNotBlank(bo.getPrintSetting()), UserSetting::getPrintSetting, bo.getPrintSetting());
        return lqw;
    }

    /**
     * 新增用户个性化设置
     *
     * @param bo 用户个性化设置
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(UserSettingBo bo) {
        UserSetting add = MapstructUtils.convert(bo, UserSetting.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改用户个性化设置
     *
     * @param bo 用户个性化设置
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(UserSettingBo bo) {
        UserSetting update = MapstructUtils.convert(bo, UserSetting.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(UserSetting entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除用户个性化设置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public UserSettingVo queryByCurrentUser() {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            throw new ServiceException("用户未登录");
        }
        String tenantId = loginUser.getTenantId();
        LambdaQueryWrapper<UserSetting> lqw = Wrappers.<UserSetting>lambdaQuery()
            .last("limit 1");
        //有可能是没有租户
        if (StringUtils.isBlank(tenantId)) {
            baseMapper.selectVoOne(lqw);
            return null;
        }
        lqw.eq(UserSetting::getTenantId, tenantId);
        return baseMapper.selectVoOne(lqw);
    }
}
