package com.qili.clinic.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qili.clinic.domain.MedicalField;
import com.qili.clinic.domain.bo.MedicalFieldBo;
import com.qili.clinic.domain.vo.MedicalFieldVo;
import com.qili.clinic.domain.vo.MedicalTemplateFieldRelVo;
import com.qili.clinic.mapper.MedicalFieldMapper;
import com.qili.clinic.service.IMedicalFieldDataService;
import com.qili.clinic.service.IMedicalFieldService;
import com.qili.clinic.service.IMedicalTemplateFieldRelService;
import com.qili.common.core.utils.MapstructUtils;
import com.qili.common.core.utils.StringUtils;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 病历字段字典Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MedicalFieldServiceImpl implements IMedicalFieldService {

    private final MedicalFieldMapper baseMapper;
    private final IMedicalTemplateFieldRelService templateFieldRelService;
    private final IMedicalFieldDataService fieldDataService;

    /**
     * 查询病历字段字典
     *
     * @param id 主键
     * @return 病历字段字典
     */
    @Override
    public MedicalFieldVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    @Override
    public List<MedicalFieldVo> queryListByIds(Collection<Long> ids) {
        return baseMapper.selectVoByIds(ids);
    }

    /**
     * 分页查询病历字段字典列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 病历字段字典分页列表
     */
    @Override
    public TableDataInfo<MedicalFieldVo> queryPageList(MedicalFieldBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MedicalField> lqw = buildQueryWrapper(bo);
        Page<MedicalFieldVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);

        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的病历字段字典列表
     *
     * @param bo 查询条件
     * @return 病历字段字典列表
     */
    @Override
    public List<MedicalFieldVo> queryList(MedicalFieldBo bo) {
        LambdaQueryWrapper<MedicalField> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MedicalField> buildQueryWrapper(MedicalFieldBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MedicalField> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(MedicalField::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getFieldCode()), MedicalField::getFieldCode, bo.getFieldCode());
        lqw.like(StringUtils.isNotBlank(bo.getFieldName()), MedicalField::getFieldName, bo.getFieldName());
        return lqw;
    }

    /**
     * 新增病历字段字典
     *
     * @param bo 病历字段字典
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MedicalFieldBo bo) {
        MedicalField add = MapstructUtils.convert(bo, MedicalField.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改病历字段字典
     *
     * @param bo 病历字段字典
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MedicalFieldBo bo) {
        MedicalField update = MapstructUtils.convert(bo, MedicalField.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MedicalField entity) {
        //新增时校验
        if (ObjectUtil.isNull(entity.getId())) {
            this.isExistFieldCode(entity.getFieldCode());
        }
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除病历字段字典信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public Map<Long, List<MedicalFieldVo>> listByTemplateIds(Collection<Long> templateIds) {
        // 查询模板字段关系列表
        List<MedicalTemplateFieldRelVo> relVos = templateFieldRelService.listByTemplateIds(templateIds);
        if (CollectionUtil.isEmpty(relVos)) {
            return Map.of();
        }
        // 根据templateId分组
        Map<Long, List<MedicalTemplateFieldRelVo>> relMap = relVos.stream()
            .collect(Collectors.groupingBy(MedicalTemplateFieldRelVo::getTemplateId));

        // 提取字段ID列表
        List<Long> fieldIds = relMap.values().stream()
            .flatMap(List::stream)
            .map(MedicalTemplateFieldRelVo::getFieldId)
            .sorted()
            .distinct()
            .toList();

        // 查询字段列表
        List<MedicalFieldVo> fieldVos = baseMapper.selectVoByIds(fieldIds);
        if (CollectionUtil.isEmpty(fieldVos)) {
            return Map.of();
        }
        // 按模板ID分组返回结果

    }

    /**
     * 校验字段编码是否存在
     *
     * @param code 字段编码
     * @return 是否存在
     */
    private boolean isExistFieldCode(String code) {
        LambdaQueryWrapper<MedicalField> lqw = Wrappers.lambdaQuery();
        lqw.eq(MedicalField::getFieldCode, code);
        return baseMapper.exists(lqw);
    }

}
