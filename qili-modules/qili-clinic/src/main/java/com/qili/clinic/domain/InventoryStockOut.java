package com.qili.clinic.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.qili.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 库存出库主对象 qili_inventory_stock_out
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("qili_inventory_stock_out")
public class InventoryStockOut extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 出库单号（自动生成如 CK202507240001）
     */
    private String stockOutNo;

    /**
     * 药房ID（qili_drug_pharmacy.id）
     */
    private Long pharmacyId;

    /**
     * 出库类型（prescription处方/manual手动/return退货）
     */
    private String type;

    /**
     * 关联ID（如处方ID）
     */
    private Long relatedId;

    /**
     * 出库总金额（分）
     */
    private Long totalAmount;

    /**
     * 出库时间
     */
    private Date stockOutTime;

    /**
     * 状态（pending待审核/approved已出库）
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0存在 1删除）
     */
    @TableLogic
    private String delFlag;


}
