package com.qili.clinic.domain.bo;

import com.qili.clinic.domain.Drug;
import com.qili.common.core.validate.AddGroup;
import com.qili.common.core.validate.EditGroup;
import com.qili.common.mybatis.core.domain.BaseEntity;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 药品信息业务对象 qili_drug
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Drug.class, reverseConvertGenerate = false)
public class DrugBo extends BaseEntity {

    /**
     * 主键ID，自增
     */
    private Long id;

    /**
     * 药品名称
     */
    private String name;

    /**
     * 别名/拼音码（支持模糊搜索）
     */
    @NotBlank(message = "别名/拼音码（支持模糊搜索）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String alias;

    /**
     * 药品编码
     */
    private String code;

    /**
     * 规格（如0.25g*10片）
     */
    private String specification;

    /**
     * 单位（g/克/片/袋/丸等）
     */
    @NotBlank(message = "单位（g/克/片/袋/丸等）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String unit;

    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 药品分类（中药/西药/成药/草药）
     */
    @NotBlank(message = "药品分类不能为空", groups = { AddGroup.class, EditGroup.class })
    private String category;

    /**
     * 单价
     */
    @NotNull(message = "单价不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long price;

    /**
     * 当前库存数量
     */
    @NotNull(message = "当前库存数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long stock;

    /**
     * 库存单位
     */
    @NotBlank(message = "库存单位不能为空", groups = { AddGroup.class, EditGroup.class })
    private String stockUnit;

    /**
     * 备注
     */
    private String remark;

}
