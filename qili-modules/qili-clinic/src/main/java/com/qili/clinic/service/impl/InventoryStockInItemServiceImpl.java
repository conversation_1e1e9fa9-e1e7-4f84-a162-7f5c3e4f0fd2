package com.qili.clinic.service.impl;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qili.clinic.domain.bo.InventoryStockInItemBo;
import com.qili.clinic.domain.InventoryStockInItem;
import com.qili.clinic.domain.vo.DrugVo;
import com.qili.clinic.domain.vo.InventoryStockInItemVo;
import com.qili.clinic.mapper.DrugMapper;
import com.qili.clinic.mapper.InventoryStockInItemMapper;
import com.qili.clinic.service.IInventoryStockInItemService;
import com.qili.common.core.utils.MapstructUtils;
import com.qili.common.core.utils.StringUtils;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 库存入库子Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class InventoryStockInItemServiceImpl implements IInventoryStockInItemService {

    private final InventoryStockInItemMapper baseMapper;
    private final DrugMapper drugMapper;

    /**
     * 查询库存入库子
     *
     * @param id 主键
     * @return 库存入库子
     */
    @Override
    public InventoryStockInItemVo queryById(Long id) {
        InventoryStockInItemVo vo = baseMapper.selectVoById(id);
        populateData(List.of(vo));
        return vo;
    }

    /**
     * 分页查询库存入库子列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 库存入库子分页列表
     */
    @Override
    public TableDataInfo<InventoryStockInItemVo> queryPageList(InventoryStockInItemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<InventoryStockInItem> lqw = buildQueryWrapper(bo);
        Page<InventoryStockInItemVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        populateData(result.getRecords());
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的库存入库子列表
     *
     * @param bo 查询条件
     * @return 库存入库子列表
     */
    @Override
    public List<InventoryStockInItemVo> queryList(InventoryStockInItemBo bo) {
        LambdaQueryWrapper<InventoryStockInItem> lqw = buildQueryWrapper(bo);
        List<InventoryStockInItemVo> list = baseMapper.selectVoList(lqw);
        populateData(list);
        return list;
    }

    private LambdaQueryWrapper<InventoryStockInItem> buildQueryWrapper(InventoryStockInItemBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<InventoryStockInItem> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(InventoryStockInItem::getId);
        lqw.eq(bo.getStockInId() != null, InventoryStockInItem::getStockInId, bo.getStockInId());
        lqw.eq(bo.getDrugId() != null, InventoryStockInItem::getDrugId, bo.getDrugId());
        lqw.eq(StringUtils.isNotBlank(bo.getBatchNo()), InventoryStockInItem::getBatchNo, bo.getBatchNo());
        lqw.eq(bo.getPurchasePrice() != null, InventoryStockInItem::getPurchasePrice, bo.getPurchasePrice());
        lqw.eq(bo.getQuantity() != null, InventoryStockInItem::getQuantity, bo.getQuantity());
        lqw.eq(bo.getAmount() != null, InventoryStockInItem::getAmount, bo.getAmount());
        return lqw;
    }

    /**
     * 新增库存入库子
     *
     * @param bo 库存入库子
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(InventoryStockInItemBo bo) {
        InventoryStockInItem add = MapstructUtils.convert(bo, InventoryStockInItem.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改库存入库子
     *
     * @param bo 库存入库子
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(InventoryStockInItemBo bo) {
        InventoryStockInItem update = MapstructUtils.convert(bo, InventoryStockInItem.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(InventoryStockInItem entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除库存入库子信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 填充药品信息
     *
     * @param voList
     */
    private void populateData(List<InventoryStockInItemVo> voList) {
        List<Long> drugIds = voList.stream().map(InventoryStockInItemVo::getDrugId).collect(Collectors.toList());
        List<DrugVo> drugList = drugMapper.selectVoByIds(drugIds);
        Map<Long, DrugVo> drugMap = drugList.stream().collect(Collectors.toMap(DrugVo::getId, Function.identity()));
        voList.forEach(vo -> {
            vo.setDrug(drugMap.get(vo.getDrugId()));
        });
    }
}
