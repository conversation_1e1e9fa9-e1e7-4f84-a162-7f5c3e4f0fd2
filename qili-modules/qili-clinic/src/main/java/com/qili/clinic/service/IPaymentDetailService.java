package com.qili.clinic.service;

import com.qili.clinic.domain.vo.PaymentDetailVo;
import com.qili.clinic.domain.bo.PaymentDetailBo;
import com.qili.common.mybatis.core.page.TableDataInfo;
import com.qili.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 支付明细Service接口
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
public interface IPaymentDetailService {

    /**
     * 查询支付明细
     *
     * @param id 主键
     * @return 支付明细
     */
    PaymentDetailVo queryById(Long id);

    /**
     * 分页查询支付明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 支付明细分页列表
     */
    TableDataInfo<PaymentDetailVo> queryPageList(PaymentDetailBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的支付明细列表
     *
     * @param bo 查询条件
     * @return 支付明细列表
     */
    List<PaymentDetailVo> queryList(PaymentDetailBo bo);

    /**
     * 新增支付明细
     *
     * @param bo 支付明细
     * @return 是否新增成功
     */
    Boolean insertByBo(PaymentDetailBo bo);

    /**
     * 修改支付明细
     *
     * @param bo 支付明细
     * @return 是否修改成功
     */
    Boolean updateByBo(PaymentDetailBo bo);

    /**
     * 校验并批量删除支付明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
