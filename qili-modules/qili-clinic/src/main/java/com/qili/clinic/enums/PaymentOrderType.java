package com.qili.clinic.enums;

import java.util.Arrays;

import lombok.Getter;

@Getter
public enum PaymentOrderType {

    REGISTRATION("REGISTRATION", "挂号"),
    PRESCRIPTION("PRESCRIPTION", "处方"),
    TREATMENT("TREATMENT", "理疗");

    private final String code;
    private final String label;

    PaymentOrderType(String code, String label) {
        this.code = code;
        this.label = label;
    }

    public static PaymentOrderType getByCode(String code) {
        return Arrays.stream(PaymentOrderType.values()).filter(type -> type.getCode().equals(code)).findFirst()
                .orElse(null);
    }
}
