package com.qili.clinic.domain.vo;

import com.qili.clinic.domain.TreatmentProject;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.qili.common.excel.annotation.ExcelDictFormat;
import com.qili.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 诊疗项目视图对象 qili_treatment_project
 *
 * <AUTHOR>
 * @date 2025-08-08
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = TreatmentProject.class)
public class TreatmentProjectVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 诊疗项目名称
     */
    @ExcelProperty(value = "诊疗项目名称")
    private String name;

    /**
     * 诊疗项目编码
     */
    @ExcelProperty(value = "诊疗项目编码")
    private String code;

    /**
     * 项目分类
     */
    @ExcelProperty(value = "项目分类", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "qili_treatment_category")
    private String category;

    /**
     * 项目说明
     */
    @ExcelProperty(value = "项目说明")
    private String description;

    /**
     * 单价
     */
    @ExcelProperty(value = "单价")
    private Long price;

    /**
     * 单位
     */
    @ExcelProperty(value = "单位", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "qili_treatment_unit")
    private String unit;

    /**
     * 预计时长
     */
    @ExcelProperty(value = "预计时长")
    private Long durationMinutes;

    /**
     * 状态（0=启用，1=停用）
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "common_yes_no")
    private String status;


}
