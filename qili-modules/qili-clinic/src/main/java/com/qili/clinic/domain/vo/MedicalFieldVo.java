package com.qili.clinic.domain.vo;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

import com.qili.clinic.domain.MedicalField;
import com.qili.common.excel.annotation.ExcelDictFormat;
import com.qili.common.excel.convert.ExcelDictConvert;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

/**
 * 病历字段字典视图对象 qili_medical_field
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MedicalField.class)
public class MedicalFieldVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 字段编码（如 chief_complaint）
     */
    @ExcelProperty(value = "字段编码", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "如=,c=hief_complaint")
    private String fieldCode;

    /**
     * 字段名称（如主诉）
     */
    @ExcelProperty(value = "字段名称", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "如=主诉")
    private String fieldName;

    /**
     * 字段类型（text/single_select/multi_select）
     */
    @ExcelProperty(value = "字段类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "t=ext/single_select/multi_select")
    private String fieldType;

    /**
     * 是否系统默认字段（1是 0否）
     */
    @ExcelProperty(value = "是否系统默认字段", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "common_yes_no")
    private String defaultFlag;

    /**
     * 是否必填
     */
    @ExcelProperty(value = "是否必填", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "common_yes_no")
    private String required;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 字段数据列表
     */
    private List<MedicalFieldDataVo> fieldDataList;

}
