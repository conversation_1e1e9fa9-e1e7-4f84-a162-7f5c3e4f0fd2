package com.qili.clinic.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.qili.clinic.domain.bo.InventoryStockInItemBo;
import com.qili.clinic.domain.vo.InventoryStockInItemVo;
import com.qili.clinic.service.IInventoryStockInItemService;
import com.qili.common.core.domain.R;
import com.qili.common.core.validate.AddGroup;
import com.qili.common.core.validate.EditGroup;
import com.qili.common.excel.utils.ExcelUtil;
import com.qili.common.idempotent.annotation.RepeatSubmit;
import com.qili.common.log.annotation.Log;
import com.qili.common.log.enums.BusinessType;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;
import com.qili.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 库存入库子
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/clinic/inventoryStockInItem")
public class InventoryStockInItemController extends BaseController {

    private final IInventoryStockInItemService inventoryStockInItemService;

    /**
     * 查询库存入库子列表
     */
    @SaCheckPermission("clinic:inventoryStockInItem:list")
    @GetMapping("/list")
    public TableDataInfo<InventoryStockInItemVo> list(InventoryStockInItemBo bo, PageQuery pageQuery) {
        return inventoryStockInItemService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出库存入库子列表
     */
    @SaCheckPermission("clinic:inventoryStockInItem:export")
    @Log(title = "库存入库子", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(InventoryStockInItemBo bo, HttpServletResponse response) {
        List<InventoryStockInItemVo> list = inventoryStockInItemService.queryList(bo);
        ExcelUtil.exportExcel(list, "库存入库子", InventoryStockInItemVo.class, response);
    }

    /**
     * 获取库存入库子详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("clinic:inventoryStockInItem:query")
    @GetMapping("/{id}")
    public R<InventoryStockInItemVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(inventoryStockInItemService.queryById(id));
    }

    /**
     * 新增库存入库子
     */
    @SaCheckPermission("clinic:inventoryStockInItem:add")
    @Log(title = "库存入库子", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody InventoryStockInItemBo bo) {
        return toAjax(inventoryStockInItemService.insertByBo(bo));
    }

    /**
     * 修改库存入库子
     */
    @SaCheckPermission("clinic:inventoryStockInItem:edit")
    @Log(title = "库存入库子", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody InventoryStockInItemBo bo) {
        return toAjax(inventoryStockInItemService.updateByBo(bo));
    }

    /**
     * 删除库存入库子
     *
     * @param ids 主键串
     */
    @SaCheckPermission("clinic:inventoryStockInItem:remove")
    @Log(title = "库存入库子", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(inventoryStockInItemService.deleteWithValidByIds(List.of(ids), true));
    }
}
