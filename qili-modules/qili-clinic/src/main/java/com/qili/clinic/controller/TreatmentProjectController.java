package com.qili.clinic.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.qili.common.idempotent.annotation.RepeatSubmit;
import com.qili.common.log.annotation.Log;
import com.qili.common.web.core.BaseController;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.core.domain.R;
import com.qili.common.core.validate.AddGroup;
import com.qili.common.core.validate.EditGroup;
import com.qili.common.log.enums.BusinessType;
import com.qili.common.excel.utils.ExcelUtil;
import com.qili.clinic.domain.vo.TreatmentProjectVo;
import com.qili.clinic.domain.bo.TreatmentProjectBo;
import com.qili.clinic.service.ITreatmentProjectService;
import com.qili.common.mybatis.core.page.TableDataInfo;

/**
 * 诊疗项目
 *
 * <AUTHOR>
 * @date 2025-08-08
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/clinic/treatmentProject")
public class TreatmentProjectController extends BaseController {

    private final ITreatmentProjectService treatmentProjectService;

    /**
     * 查询诊疗项目列表
     */
    @SaCheckPermission("clinic:treatmentProject:list")
    @GetMapping("/list")
    public TableDataInfo<TreatmentProjectVo> list(TreatmentProjectBo bo, PageQuery pageQuery) {
        return treatmentProjectService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出诊疗项目列表
     */
    @SaCheckPermission("clinic:treatmentProject:export")
    @Log(title = "诊疗项目", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(TreatmentProjectBo bo, HttpServletResponse response) {
        List<TreatmentProjectVo> list = treatmentProjectService.queryList(bo);
        ExcelUtil.exportExcel(list, "诊疗项目", TreatmentProjectVo.class, response);
    }

    /**
     * 获取诊疗项目详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("clinic:treatmentProject:query")
    @GetMapping("/{id}")
    public R<TreatmentProjectVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(treatmentProjectService.queryById(id));
    }

    /**
     * 新增诊疗项目
     */
    @SaCheckPermission("clinic:treatmentProject:add")
    @Log(title = "诊疗项目", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody TreatmentProjectBo bo) {
        return toAjax(treatmentProjectService.insertByBo(bo));
    }

    /**
     * 修改诊疗项目
     */
    @SaCheckPermission("clinic:treatmentProject:edit")
    @Log(title = "诊疗项目", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody TreatmentProjectBo bo) {
        return toAjax(treatmentProjectService.updateByBo(bo));
    }

    /**
     * 删除诊疗项目
     *
     * @param ids 主键串
     */
    @SaCheckPermission("clinic:treatmentProject:remove")
    @Log(title = "诊疗项目", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(treatmentProjectService.deleteWithValidByIds(List.of(ids), true));
    }
}
