package com.qili.clinic.service;

import com.qili.clinic.domain.bo.MedicalTemplateFieldRelBo;
import com.qili.clinic.domain.vo.MedicalTemplateFieldRelVo;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 病历模板字段Service接口
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
public interface IMedicalTemplateFieldRelService {

    /**
     * 查询病历模板字段
     *
     * @param id 主键
     * @return 病历模板字段
     */
    MedicalTemplateFieldRelVo queryById(Long id);

    /**
     * 分页查询病历模板字段列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 病历模板字段分页列表
     */
    TableDataInfo<MedicalTemplateFieldRelVo> queryPageList(MedicalTemplateFieldRelBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的病历模板字段列表
     *
     * @param bo 查询条件
     * @return 病历模板字段列表
     */
    List<MedicalTemplateFieldRelVo> queryList(MedicalTemplateFieldRelBo bo);

    /**
     * 新增病历模板字段
     *
     * @param bo 病历模板字段
     * @return 是否新增成功
     */
    Boolean insertByBo(MedicalTemplateFieldRelBo bo);

    /**
     * 修改病历模板字段
     *
     * @param bo 病历模板字段
     * @return 是否修改成功
     */
    Boolean updateByBo(MedicalTemplateFieldRelBo bo);

    /**
     * 校验并批量删除病历模板字段信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据模板ID查询模板字段关系列表
     *
     * @param templateIds 模板ID列表
     * @return 模板字段关系列表
     */
    List<MedicalTemplateFieldRelVo> listByTemplateIds(Collection<Long> templateIds);
}
