package com.qili.clinic.domain.bo;

import com.qili.clinic.domain.MedicalTemplateFieldRel;
import com.qili.common.core.validate.AddGroup;
import com.qili.common.core.validate.EditGroup;
import com.qili.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.*;

/**
 * 病历模板字段业务对象 qili_medical_template_field_rel
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MedicalTemplateFieldRel.class, reverseConvertGenerate = false)
@AllArgsConstructor
@NoArgsConstructor
public class MedicalTemplateFieldRelBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 模板ID（关联qili_medical_template.id）
     */
    private Long templateId;

    /**
     * 字段ID（关联qili_medical_field.id）
     */
    private Long fieldId;

    /**
     * 字段显示顺序
     */
    @NotNull(message = "字段显示顺序不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long sortOrder;


}
