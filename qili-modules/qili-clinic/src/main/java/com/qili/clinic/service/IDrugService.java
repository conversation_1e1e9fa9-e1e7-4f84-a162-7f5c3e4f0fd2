package com.qili.clinic.service;

import com.qili.clinic.domain.bo.DrugBo;
import com.qili.clinic.domain.vo.DrugVo;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 药品信息Service接口
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface IDrugService {

    /**
     * 查询药品信息
     *
     * @param id 主键
     * @return 药品信息
     */
    DrugVo queryById(Long id);

    /**
     * 分页查询药品信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 药品信息分页列表
     */
    TableDataInfo<DrugVo> queryPageList(DrugBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的药品信息列表
     *
     * @param bo 查询条件
     * @return 药品信息列表
     */
    List<DrugVo> queryList(DrugBo bo);

    /**
     * 新增药品信息
     *
     * @param bo 药品信息
     * @return 是否新增成功
     */
    Boolean insertByBo(DrugBo bo);

    /**
     * 修改药品信息
     *
     * @param bo 药品信息
     * @return 是否修改成功
     */
    Boolean updateByBo(DrugBo bo);

    /**
     * 校验并批量删除药品信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
