package com.qili.clinic.service;

import com.qili.clinic.domain.vo.UserSettingVo;
import com.qili.clinic.domain.bo.UserSettingBo;
import com.qili.common.mybatis.core.page.TableDataInfo;
import com.qili.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 用户个性化设置Service接口
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
public interface IUserSettingService {

    /**
     * 查询用户个性化设置
     *
     * @param id 主键
     * @return 用户个性化设置
     */
    UserSettingVo queryById(Long id);

    /**
     * 分页查询用户个性化设置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 用户个性化设置分页列表
     */
    TableDataInfo<UserSettingVo> queryPageList(UserSettingBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的用户个性化设置列表
     *
     * @param bo 查询条件
     * @return 用户个性化设置列表
     */
    List<UserSettingVo> queryList(UserSettingBo bo);

    /**
     * 新增用户个性化设置
     *
     * @param bo 用户个性化设置
     * @return 是否新增成功
     */
    Boolean insertByBo(UserSettingBo bo);

    /**
     * 修改用户个性化设置
     *
     * @param bo 用户个性化设置
     * @return 是否修改成功
     */
    Boolean updateByBo(UserSettingBo bo);

    /**
     * 校验并批量删除用户个性化设置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    UserSettingVo queryByCurrentUser();
}
