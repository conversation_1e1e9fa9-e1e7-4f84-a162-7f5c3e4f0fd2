package com.qili.clinic.domain.vo;

import com.qili.clinic.domain.Pharmacy;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.qili.common.excel.annotation.ExcelDictFormat;
import com.qili.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 药房信息视图对象 qili_pharmacy
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Pharmacy.class)
public class PharmacyVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 药房名称
     */
    @ExcelProperty(value = "药房名称")
    private String name;

    /**
     * 药房编码
     */
    @ExcelProperty(value = "药房编码")
    private String code;

    /**
     * 负责人
     */
    @ExcelProperty(value = "负责人")
    private String contactPerson;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "qili_pharmacy_status")
    private String status;


}
