package com.qili.clinic.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.qili.clinic.domain.bo.InventoryStockOutBo;
import com.qili.clinic.domain.vo.InventoryStockOutVo;
import com.qili.clinic.service.IInventoryStockOutService;
import com.qili.common.core.domain.R;
import com.qili.common.core.validate.AddGroup;
import com.qili.common.core.validate.EditGroup;
import com.qili.common.excel.utils.ExcelUtil;
import com.qili.common.idempotent.annotation.RepeatSubmit;
import com.qili.common.log.annotation.Log;
import com.qili.common.log.enums.BusinessType;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;
import com.qili.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 库存出库主
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/clinic/inventoryStockOut")
public class InventoryStockOutController extends BaseController {

    private final IInventoryStockOutService inventoryStockOutService;

    /**
     * 查询库存出库主列表
     */
    @SaCheckPermission("clinic:inventoryStockOut:list")
    @GetMapping("/list")
    public TableDataInfo<InventoryStockOutVo> list(InventoryStockOutBo bo, PageQuery pageQuery) {
        return inventoryStockOutService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出库存出库主列表
     */
    @SaCheckPermission("clinic:inventoryStockOut:export")
    @Log(title = "库存出库主", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(InventoryStockOutBo bo, HttpServletResponse response) {
        List<InventoryStockOutVo> list = inventoryStockOutService.queryList(bo);
        ExcelUtil.exportExcel(list, "库存出库主", InventoryStockOutVo.class, response);
    }

    /**
     * 获取库存出库主详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("clinic:inventoryStockOut:query")
    @GetMapping("/{id}")
    public R<InventoryStockOutVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(inventoryStockOutService.queryById(id));
    }

    /**
     * 新增库存出库主
     */
    @SaCheckPermission("clinic:inventoryStockOut:add")
    @Log(title = "库存出库主", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody InventoryStockOutBo bo) {
        return toAjax(inventoryStockOutService.insertByBo(bo));
    }

    /**
     * 修改库存出库主
     */
    @SaCheckPermission("clinic:inventoryStockOut:edit")
    @Log(title = "库存出库主", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody InventoryStockOutBo bo) {
        return toAjax(inventoryStockOutService.updateByBo(bo));
    }

    /**
     * 删除库存出库主
     *
     * @param ids 主键串
     */
    @SaCheckPermission("clinic:inventoryStockOut:remove")
    @Log(title = "库存出库主", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(inventoryStockOutService.deleteWithValidByIds(List.of(ids), true));
    }
}
