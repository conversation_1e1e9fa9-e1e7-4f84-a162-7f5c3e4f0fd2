package com.qili.clinic.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.qili.clinic.domain.bo.PatientBo;
import com.qili.clinic.domain.vo.PatientVo;
import com.qili.clinic.service.IPatientService;
import com.qili.common.core.domain.R;
import com.qili.common.core.validate.AddGroup;
import com.qili.common.core.validate.EditGroup;
import com.qili.common.excel.utils.ExcelUtil;
import com.qili.common.idempotent.annotation.RepeatSubmit;
import com.qili.common.log.annotation.Log;
import com.qili.common.log.enums.BusinessType;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;
import com.qili.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 患者档案
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/clinic/patient")
public class PatientController extends BaseController {

    private final IPatientService qiliPatientsService;

    /**
     * 查询患者档案列表
     */
    @SaCheckPermission("clinic:patient:list")
    @GetMapping("/list")
    public TableDataInfo<PatientVo> list(PatientBo bo, PageQuery pageQuery) {
        return qiliPatientsService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出患者档案列表
     */
    @SaCheckPermission("clinic:patient:export")
    @Log(title = "患者档案", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PatientBo bo, HttpServletResponse response) {
        List<PatientVo> list = qiliPatientsService.queryList(bo);
        ExcelUtil.exportExcel(list, "患者档案", PatientVo.class, response);
    }

    /**
     * 获取患者档案详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("clinic:patient:query")
    @GetMapping("/{id}")
    public R<PatientVo> getInfo(@NotNull(message = "主键不能为空")
                                @PathVariable Long id) {
        return R.ok(qiliPatientsService.queryById(id));
    }

    /**
     * 新增患者档案
     */
    @SaCheckPermission("clinic:patient:add")
    @Log(title = "患者档案", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<PatientVo> add(@Validated(AddGroup.class) @RequestBody PatientBo bo) {
        PatientVo patientVo = qiliPatientsService.insertByBo(bo);
        return R.ok("新增患者成功",patientVo);
    }

    /**
     * 修改患者档案
     */
    @SaCheckPermission("clinic:patient:edit")
    @Log(title = "患者档案", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PatientBo bo) {
        return toAjax(qiliPatientsService.updateByBo(bo));
    }

    /**
     * 删除患者档案
     *
     * @param ids 主键串
     */
    @SaCheckPermission("clinic:patient:remove")
    @Log(title = "患者档案", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(qiliPatientsService.deleteWithValidByIds(List.of(ids), true));
    }
}
