package com.qili.clinic.service;

import com.qili.clinic.domain.bo.InventoryStockInItemBo;
import com.qili.clinic.domain.vo.InventoryStockInItemVo;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 库存入库子Service接口
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface IInventoryStockInItemService {

    /**
     * 查询库存入库子
     *
     * @param id 主键
     * @return 库存入库子
     */
    InventoryStockInItemVo queryById(Long id);

    /**
     * 分页查询库存入库子列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 库存入库子分页列表
     */
    TableDataInfo<InventoryStockInItemVo> queryPageList(InventoryStockInItemBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的库存入库子列表
     *
     * @param bo 查询条件
     * @return 库存入库子列表
     */
    List<InventoryStockInItemVo> queryList(InventoryStockInItemBo bo);

    /**
     * 新增库存入库子
     *
     * @param bo 库存入库子
     * @return 是否新增成功
     */
    Boolean insertByBo(InventoryStockInItemBo bo);

    /**
     * 修改库存入库子
     *
     * @param bo 库存入库子
     * @return 是否修改成功
     */
    Boolean updateByBo(InventoryStockInItemBo bo);

    /**
     * 校验并批量删除库存入库子信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
