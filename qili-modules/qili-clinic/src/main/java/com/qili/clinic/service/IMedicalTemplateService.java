package com.qili.clinic.service;

import com.qili.clinic.domain.bo.MedicalTemplateBo;
import com.qili.clinic.domain.vo.MedicalTemplateVo;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 病历模板Service接口
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
public interface IMedicalTemplateService {

    /**
     * 查询病历模板
     *
     * @param id 主键
     * @return 病历模板
     */
    MedicalTemplateVo queryById(Long id);

    /**
     * 分页查询病历模板列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 病历模板分页列表
     */
    TableDataInfo<MedicalTemplateVo> queryPageList(MedicalTemplateBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的病历模板列表
     *
     * @param bo 查询条件
     * @return 病历模板列表
     */
    List<MedicalTemplateVo> queryList(MedicalTemplateBo bo);

    /**
     * 新增病历模板
     *
     * @param bo 病历模板
     * @return 是否新增成功
     */
    Boolean insertByBo(MedicalTemplateBo bo);

    /**
     * 修改病历模板
     *
     * @param bo 病历模板
     * @return 是否修改成功
     */
    Boolean updateByBo(MedicalTemplateBo bo);

    /**
     * 校验并批量删除病历模板信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 获取默认模板
     *
     * @return 默认模板
     */
    MedicalTemplateVo queryDefaultTemplate();
}
