package com.qili.clinic.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.qili.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 病历字段字典对象 qili_medical_field
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("qili_medical_field")
public class MedicalField extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 字段编码（如 chief_complaint）
     */
    private String fieldCode;

    /**
     * 字段名称（如主诉）
     */
    private String fieldName;

    /**
     * 字段类型（text/single_select/multi_select）
     */
    private String fieldType;

    /**
     * 是否系统默认字段（1是 0否）
     */
    private String defaultFlag;

    /**
     * 是否必填
     */
    private String required;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;


}
