package com.qili.clinic.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.qili.common.idempotent.annotation.RepeatSubmit;
import com.qili.common.log.annotation.Log;
import com.qili.common.web.core.BaseController;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.core.domain.R;
import com.qili.common.core.validate.AddGroup;
import com.qili.common.core.validate.EditGroup;
import com.qili.common.log.enums.BusinessType;
import com.qili.common.excel.utils.ExcelUtil;
import com.qili.clinic.domain.vo.MedicalRecordLogVo;
import com.qili.clinic.domain.bo.MedicalRecordLogBo;
import com.qili.clinic.service.IMedicalRecordLogService;
import com.qili.common.mybatis.core.page.TableDataInfo;

/**
 * 病历状态变更日志
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/clinic/medicalRecordLog")
public class MedicalRecordLogController extends BaseController {

    private final IMedicalRecordLogService medicalRecordLogService;

    /**
     * 查询病历状态变更日志列表
     */
    @SaCheckPermission("clinic:medicalRecordLog:list")
    @GetMapping("/list")
    public TableDataInfo<MedicalRecordLogVo> list(MedicalRecordLogBo bo, PageQuery pageQuery) {
        return medicalRecordLogService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出病历状态变更日志列表
     */
    @SaCheckPermission("clinic:medicalRecordLog:export")
    @Log(title = "病历状态变更日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MedicalRecordLogBo bo, HttpServletResponse response) {
        List<MedicalRecordLogVo> list = medicalRecordLogService.queryList(bo);
        ExcelUtil.exportExcel(list, "病历状态变更日志", MedicalRecordLogVo.class, response);
    }

    /**
     * 获取病历状态变更日志详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("clinic:medicalRecordLog:query")
    @GetMapping("/{id}")
    public R<MedicalRecordLogVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(medicalRecordLogService.queryById(id));
    }

    /**
     * 新增病历状态变更日志
     */
    @SaCheckPermission("clinic:medicalRecordLog:add")
    @Log(title = "病历状态变更日志", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MedicalRecordLogBo bo) {
        return toAjax(medicalRecordLogService.insertByBo(bo));
    }

    /**
     * 修改病历状态变更日志
     */
    @SaCheckPermission("clinic:medicalRecordLog:edit")
    @Log(title = "病历状态变更日志", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MedicalRecordLogBo bo) {
        return toAjax(medicalRecordLogService.updateByBo(bo));
    }

    /**
     * 删除病历状态变更日志
     *
     * @param ids 主键串
     */
    @SaCheckPermission("clinic:medicalRecordLog:remove")
    @Log(title = "病历状态变更日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(medicalRecordLogService.deleteWithValidByIds(List.of(ids), true));
    }
}
