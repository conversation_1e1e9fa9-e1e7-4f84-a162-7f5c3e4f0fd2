package com.qili.clinic.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.qili.clinic.domain.InventoryStockOut;
import com.qili.common.excel.annotation.ExcelDictFormat;
import com.qili.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 库存出库主视图对象 qili_inventory_stock_out
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = InventoryStockOut.class)
public class InventoryStockOutVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 出库单号（自动生成如 CK202507240001）
     */
    @ExcelProperty(value = "出库单号", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "自=动生成如,C=K202507240001")
    private String stockOutNo;

    /**
     * 药房ID（qili_drug_pharmacy.id）
     */
    @ExcelProperty(value = "药房ID", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "q=ili_drug_pharmacy.id")
    private Long pharmacyId;

    /**
     * 出库类型（prescription处方/manual手动/return退货）
     */
    @ExcelProperty(value = "出库类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "qili_inventory_stock_out_type")
    private String type;

    /**
     * 关联ID（如处方ID）
     */
    @ExcelProperty(value = "关联ID", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "如=处方ID")
    private Long relatedId;

    /**
     * 出库总金额（分）
     */
    @ExcelProperty(value = "出库总金额", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "分=")
    private Long totalAmount;

    /**
     * 出库时间
     */
    @ExcelProperty(value = "出库时间")
    private Date stockOutTime;

    /**
     * 状态（pending待审核/approved已出库）
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "p=ending待审核/approved已出库")
    private String status;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
