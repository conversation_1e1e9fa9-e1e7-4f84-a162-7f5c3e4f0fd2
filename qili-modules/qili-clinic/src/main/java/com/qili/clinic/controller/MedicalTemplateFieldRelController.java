package com.qili.clinic.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.qili.clinic.domain.bo.MedicalTemplateFieldRelBo;
import com.qili.clinic.domain.vo.MedicalTemplateFieldRelVo;
import com.qili.clinic.service.IMedicalTemplateFieldRelService;
import com.qili.common.core.domain.R;
import com.qili.common.core.validate.AddGroup;
import com.qili.common.core.validate.EditGroup;
import com.qili.common.excel.utils.ExcelUtil;
import com.qili.common.idempotent.annotation.RepeatSubmit;
import com.qili.common.log.annotation.Log;
import com.qili.common.log.enums.BusinessType;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;
import com.qili.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 病历模板字段
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/clinic/medicalTemplateFieldRel")
public class MedicalTemplateFieldRelController extends BaseController {

    private final IMedicalTemplateFieldRelService medicalTemplateFieldRelService;

    /**
     * 查询病历模板字段列表
     */
    @SaCheckPermission("clinic:medicalTemplateFieldRel:list")
    @GetMapping("/list")
    public TableDataInfo<MedicalTemplateFieldRelVo> list(MedicalTemplateFieldRelBo bo, PageQuery pageQuery) {
        return medicalTemplateFieldRelService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出病历模板字段列表
     */
    @SaCheckPermission("clinic:medicalTemplateFieldRel:export")
    @Log(title = "病历模板字段", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MedicalTemplateFieldRelBo bo, HttpServletResponse response) {
        List<MedicalTemplateFieldRelVo> list = medicalTemplateFieldRelService.queryList(bo);
        ExcelUtil.exportExcel(list, "病历模板字段", MedicalTemplateFieldRelVo.class, response);
    }

    /**
     * 获取病历模板字段详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("clinic:medicalTemplateFieldRel:query")
    @GetMapping("/{id}")
    public R<MedicalTemplateFieldRelVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(medicalTemplateFieldRelService.queryById(id));
    }

    /**
     * 新增病历模板字段
     */
    @SaCheckPermission("clinic:medicalTemplateFieldRel:add")
    @Log(title = "病历模板字段", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MedicalTemplateFieldRelBo bo) {
        return toAjax(medicalTemplateFieldRelService.insertByBo(bo));
    }

    /**
     * 修改病历模板字段
     */
    @SaCheckPermission("clinic:medicalTemplateFieldRel:edit")
    @Log(title = "病历模板字段", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MedicalTemplateFieldRelBo bo) {
        return toAjax(medicalTemplateFieldRelService.updateByBo(bo));
    }

    /**
     * 删除病历模板字段
     *
     * @param ids 主键串
     */
    @SaCheckPermission("clinic:medicalTemplateFieldRel:remove")
    @Log(title = "病历模板字段", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(medicalTemplateFieldRelService.deleteWithValidByIds(List.of(ids), true));
    }
}
