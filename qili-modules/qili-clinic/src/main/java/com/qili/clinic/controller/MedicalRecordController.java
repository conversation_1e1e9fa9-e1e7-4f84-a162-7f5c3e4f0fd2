package com.qili.clinic.controller;

import java.util.List;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.qili.clinic.domain.bo.MedicalRecordBo;
import com.qili.clinic.domain.vo.MedicalRecordVo;
import com.qili.clinic.service.IMedicalRecordService;
import com.qili.common.core.domain.R;
import com.qili.common.core.validate.AddGroup;
import com.qili.common.core.validate.EditGroup;
import com.qili.common.excel.utils.ExcelUtil;
import com.qili.common.idempotent.annotation.RepeatSubmit;
import com.qili.common.log.annotation.Log;
import com.qili.common.log.enums.BusinessType;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;
import com.qili.common.web.core.BaseController;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;

/**
 * 病历/就诊记录
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/clinic/medicalRecord")
public class MedicalRecordController extends BaseController {

    private final IMedicalRecordService medicalRecordService;

    /**
     * 查询病历/就诊记录列表
     */
    @SaCheckPermission("clinic:medicalRecord:list")
    @GetMapping("/list")
    public TableDataInfo<MedicalRecordVo> list(MedicalRecordBo bo, PageQuery pageQuery) {
        return medicalRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出病历/就诊记录列表
     */
    @SaCheckPermission("clinic:medicalRecord:export")
    @Log(title = "病历/就诊记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MedicalRecordBo bo, HttpServletResponse response) {
        List<MedicalRecordVo> list = medicalRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "病历/就诊记录", MedicalRecordVo.class, response);
    }

    /**
     * 获取病历/就诊记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("clinic:medicalRecord:query")
    @GetMapping("/{id}")
    public R<MedicalRecordVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(medicalRecordService.queryById(id));
    }

    /**
     * 新增病历/就诊记录
     */
    @SaCheckPermission("clinic:medicalRecord:add")
    @Log(title = "病历/就诊记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Long> add(@Validated(AddGroup.class) @RequestBody MedicalRecordBo bo) {
        return R.ok(medicalRecordService.insertByBo(bo));
    }

    /**
     * 修改病历/就诊记录
     */
    @SaCheckPermission("clinic:medicalRecord:edit")
    @Log(title = "病历/就诊记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MedicalRecordBo bo) {
        return toAjax(medicalRecordService.updateByBo(bo));
    }

    /**
     * 删除病历/就诊记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("clinic:medicalRecord:remove")
    @Log(title = "病历/就诊记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(medicalRecordService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 取消就诊
     *
     * @param id 主键
     * @return 结果
     */
    @SaCheckPermission("clinic:medicalRecord:edit")
    @Log(title = "病历/就诊记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/cancel/{id}")
    public R<Void> cancel(@PathVariable Long id) {
        medicalRecordService.cancelById(id);
        return R.ok("取消成功");
    }

    /**
     * 完成就诊
     *
     * @param id 主键
     * @return 结果
     */
    @SaCheckPermission("clinic:medicalRecord:edit")
    @Log(title = "病历/就诊记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/complete")
    public R<Void> complete(@RequestBody MedicalRecordBo bo) {
        medicalRecordService.complete(bo);
        return R.ok("已完成接诊");
    }
}
