package com.qili.clinic.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.qili.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 库存入库子对象 qili_inventory_stock_in_item
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("qili_inventory_stock_in_item")
public class InventoryStockInItem extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 入库单ID（关联qili_inventory_stock_in.id）
     */
    private Long stockInId;

    /**
     * 药品ID（qili_drug.id）
     */
    private Long drugId;

    /**
     * 药品名称
     */
    private String drugName;

    /**
     * 批次号（如20250724-001）
     */
    private String batchNo;

    /**
     * 进价（分）
     */
    private Long purchasePrice;

    /**
     * 入库数量
     */
    private Long quantity;

    /**
     * 小计金额（分）
     */
    private Long amount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0存在 1删除）
     */
    @TableLogic
    private String delFlag;


}
