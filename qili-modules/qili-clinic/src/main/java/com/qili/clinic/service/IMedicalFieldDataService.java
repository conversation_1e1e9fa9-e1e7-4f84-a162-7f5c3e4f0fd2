package com.qili.clinic.service;

import com.qili.clinic.domain.bo.MedicalFieldDataBo;
import com.qili.clinic.domain.vo.MedicalFieldDataGroupVo;
import com.qili.clinic.domain.vo.MedicalFieldDataVo;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 病历字段值Service接口
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
public interface IMedicalFieldDataService {

    /**
     * 查询病历字段值
     *
     * @param id 主键
     * @return 病历字段值
     */
    MedicalFieldDataVo queryById(Long id);

    /**
     * 分页查询病历字段值列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 病历字段值分页列表
     */
    TableDataInfo<MedicalFieldDataVo> queryPageList(MedicalFieldDataBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的病历字段值列表
     *
     * @param bo 查询条件
     * @return 病历字段值列表
     */
    List<MedicalFieldDataVo> queryList(MedicalFieldDataBo bo);

    /**
     * 新增病历字段值
     *
     * @param bo 病历字段值
     * @return 是否新增成功
     */
    Boolean insertByBo(MedicalFieldDataBo bo);

    /**
     * 修改病历字段值
     *
     * @param bo 病历字段值
     * @return 是否修改成功
     */
    Boolean updateByBo(MedicalFieldDataBo bo);

    /**
     * 校验并批量删除病历字段值信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据字段ID查询病历字段值列表
     *
     * @param fieldId 字段ID
     * @return 病历字段值列表
     */
    List<MedicalFieldDataGroupVo> queryListByFieldId(Long fieldId);

    /**
     * 根据字段ID集合查询病历字段值列表
     *
     * @param fieldIds 字段ID集合
     * @return 病历字段值列表
     */
    List<MedicalFieldDataVo> queryListByFields(Collection<Long> fieldIds);
}
