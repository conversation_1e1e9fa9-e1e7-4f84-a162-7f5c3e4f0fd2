package com.qili.clinic.domain;

import com.qili.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

import java.io.Serial;

/**
 * 病历/就诊记录对象 qili_medical_record
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("qili_medical_record")
public class MedicalRecord extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 诊号
     */
    private String visitNo;

    /**
     * 患者ID
     */
    private Long patientId;

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 医生ID
     */
    private Long doctorId;

    /**
     * 医生姓名
     */
    private String doctorName;

    /**
     * 科室ID
     */
    private Long deptId;

    /**
     * 就诊状态
     */
    private String status;

    /**
     * 就诊时间
     */
    private Date visitTime;

    /**
     * 诊断快照
     */
    private String diagnosisJson;

    /**
     * 中药饮片处方快照
     */
    private String tcmPrescriptionJson;

    /**
     * 中成药处方快照
     */
    private String tcmpPrescriptionJson;

    /**
     * 西药处方快照
     */
    private String wmPrescriptionJson;

    /**
     * 诊疗项目处方
     */
    private String treatmentItemJson;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0=存在 1=删除）
     */
    @TableLogic
    private String delFlag;


}
