package com.qili.clinic.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.qili.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 药品信息对象 qili_drug
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("qili_drug")
public class Drug extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID，自增
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 药品名称
     */
    private String name;

    /**
     * 别名/拼音码（支持模糊搜索）
     */
    private String alias;

    /**
     * 药品编码
     */
    private String code;

    /**
     * 规格（如0.25g*10片）
     */
    private String specification;

    /**
     * 单位（g/克/片/袋/丸等）
     */
    private String unit;

    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 药品分类（中药/西药/成药/草药）
     */
    private String category;

    /**
     * 单价（元）
     */
    private Long price;

    /**
     * 当前库存数量
     */
    private Long stock;

    /**
     * 库存单位（克/剂/盒等）
     */
    private String stockUnit;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;


}
