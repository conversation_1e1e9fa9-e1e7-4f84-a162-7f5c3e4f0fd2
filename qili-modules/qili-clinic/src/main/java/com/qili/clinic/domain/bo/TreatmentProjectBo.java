package com.qili.clinic.domain.bo;

import com.qili.clinic.domain.TreatmentProject;
import com.qili.common.mybatis.core.domain.BaseEntity;
import com.qili.common.core.validate.AddGroup;
import com.qili.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 诊疗项目业务对象 qili_treatment_project
 *
 * <AUTHOR>
 * @date 2025-08-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = TreatmentProject.class, reverseConvertGenerate = false)
public class TreatmentProjectBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 诊疗项目名称
     */
    private String name;

    /**
     * 诊疗项目编码
     */
    private String code;

    /**
     * 项目分类
     */
    @NotBlank(message = "项目分类不能为空", groups = { AddGroup.class, EditGroup.class })
    private String category;

    /**
     * 项目说明
     */
    private String description;

    /**
     * 单价
     */
    @NotNull(message = "单价不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long price;

    /**
     * 单位
     */
    @NotBlank(message = "单位不能为空", groups = { AddGroup.class, EditGroup.class })
    private String unit;

    /**
     * 预计时长
     */
    private Long durationMinutes;

    /**
     * 状态（0=启用，1=停用）
     */
    @NotBlank(message = "状态（0=启用，1=停用）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;


}
