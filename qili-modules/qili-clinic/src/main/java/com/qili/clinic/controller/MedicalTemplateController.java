package com.qili.clinic.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.qili.clinic.domain.bo.MedicalTemplateBo;
import com.qili.clinic.domain.vo.MedicalTemplateVo;
import com.qili.clinic.service.IMedicalTemplateService;
import com.qili.common.core.domain.R;
import com.qili.common.core.validate.AddGroup;
import com.qili.common.core.validate.EditGroup;
import com.qili.common.excel.utils.ExcelUtil;
import com.qili.common.idempotent.annotation.RepeatSubmit;
import com.qili.common.log.annotation.Log;
import com.qili.common.log.enums.BusinessType;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;
import com.qili.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 病历模板
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/clinic/medicalTemplate")
public class MedicalTemplateController extends BaseController {

    private final IMedicalTemplateService medicalTemplateService;

    /**
     * 查询病历模板列表
     */
    @SaCheckPermission("clinic:medicalTemplate:list")
    @GetMapping("/list")
    public TableDataInfo<MedicalTemplateVo> list(MedicalTemplateBo bo, PageQuery pageQuery) {
        return medicalTemplateService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出病历模板列表
     */
    @SaCheckPermission("clinic:medicalTemplate:export")
    @Log(title = "病历模板", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MedicalTemplateBo bo, HttpServletResponse response) {
        List<MedicalTemplateVo> list = medicalTemplateService.queryList(bo);
        ExcelUtil.exportExcel(list, "病历模板", MedicalTemplateVo.class, response);
    }

    /**
     * 获取病历模板详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("clinic:medicalTemplate:query")
    @GetMapping("/{id}")
    public R<MedicalTemplateVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(medicalTemplateService.queryById(id));
    }

    /**
     * 新增病历模板
     */
    @SaCheckPermission("clinic:medicalTemplate:add")
    @Log(title = "病历模板", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MedicalTemplateBo bo) {
        return toAjax(medicalTemplateService.insertByBo(bo));
    }

    /**
     * 修改病历模板
     */
    @SaCheckPermission("clinic:medicalTemplate:edit")
    @Log(title = "病历模板", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MedicalTemplateBo bo) {
        return toAjax(medicalTemplateService.updateByBo(bo));
    }

    /**
     * 删除病历模板
     *
     * @param ids 主键串
     */
    @SaCheckPermission("clinic:medicalTemplate:remove")
    @Log(title = "病历模板", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(medicalTemplateService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 获取默认模板
     */
    @SaCheckPermission("clinic:medicalTemplate:query")
    @GetMapping("/default")
    public R<MedicalTemplateVo> getDefaultTemplate() {
        return R.ok(medicalTemplateService.queryDefaultTemplate());
    }

}
