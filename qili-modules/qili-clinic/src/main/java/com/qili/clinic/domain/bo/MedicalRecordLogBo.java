package com.qili.clinic.domain.bo;

import com.qili.clinic.domain.MedicalRecordLog;
import com.qili.common.mybatis.core.domain.BaseEntity;
import com.qili.common.core.validate.AddGroup;
import com.qili.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;

/**
 * 病历状态变更日志业务对象 qili_medical_record_log
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MedicalRecordLog.class, reverseConvertGenerate = false)
public class MedicalRecordLogBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 病历ID（关联qili_medical_record.id）
     */
    private Long recordId;

    /**
     * 操作状态（WAITING=待接诊, IN_PROGRESS=就诊中, PRESCRIPTION_DONE=处方已开, DISPENSING=取药中, DECOCTING=煎药中, COMPLETED=已完成, CANCELLED=已取消）
     */
    private String action;

    /**
     * 操作名称（冗余中文，如 待接诊、就诊中）
     */
    @NotBlank(message = "操作名称（冗余中文，如 待接诊、就诊中）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String actionName;

    /**
     * 操作时间
     */
    @NotNull(message = "操作时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date actionTime;


}
