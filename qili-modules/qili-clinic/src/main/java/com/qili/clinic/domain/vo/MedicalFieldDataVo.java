package com.qili.clinic.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.qili.clinic.domain.MedicalFieldData;
import com.qili.common.excel.annotation.ExcelDictFormat;
import com.qili.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 病历字段值视图对象 qili_medical_field_data
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ExcelIgnoreUnannotated
@AutoMapper(target = MedicalFieldData.class)
public class MedicalFieldDataVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 分组名称
     */
    @ExcelProperty(value = "分组名称")
    private String groupName;

    /**
     * 分组顺序
     */
    @ExcelProperty(value = "分组顺序")
    private int groupSortOrder;

    /**
     * 字段ID（关联qili_medical_field.id）
     */
    @ExcelProperty(value = "字段ID", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "关=联qili_medical_field.id")
    private Long fieldId;

    /**
     * 字段值（如 咳嗽）
     */
    @ExcelProperty(value = "字段值", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "如=,咳=嗽")
    private String data;

    /**
     * 显示顺序
     */
    @ExcelProperty(value = "显示顺序")
    private Long sortOrder;

}
