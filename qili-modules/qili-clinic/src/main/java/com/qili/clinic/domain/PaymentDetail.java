package com.qili.clinic.domain;

import com.qili.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 支付明细对象 qili_payment_detail
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("qili_payment_detail")
public class PaymentDetail extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 支付单ID（关联qili_payment.id）
     */
    private Long paymentId;

    /**
     * 药品ID（关联qili_drug.id，非药品项目可为空）
     */
    private Long drugId;

    /**
     * 收费项目名称（如挂号费、针灸、汤药）
     */
    private String itemName;

    /**
     * 收费项目编码
     */
    private String itemCode;

    /**
     * 数量
     */
    private Long quantity;

    /**
     * 单价（分）
     */
    private Long price;

    /**
     * 单价单位
     */
    private String unit;

    /**
     * 小计金额（分）
     */
    private Long amount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;


}
