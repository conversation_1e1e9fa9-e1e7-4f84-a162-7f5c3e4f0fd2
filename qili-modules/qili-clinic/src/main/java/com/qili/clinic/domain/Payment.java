package com.qili.clinic.domain;

import com.qili.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

import java.io.Serial;

/**
 * 支付主对象 qili_payment
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("qili_payment")
public class Payment extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 支付单号（如 ZF202508140001）
     */
    private String paymentNo;

    /**
     * 患者ID（关联患者表）
     */
    private Long patientId;

    /**
     * 业务类型（registration挂号/prescription处方/treatment理疗等）
     */
    private String orderType;
    /**
     * 业务ID
     */
    private Long sourceId;

    /**
     * 应付总金额（分）
     */
    private Long totalAmount;

    /**
     * 实付金额（分）
     */
    private Long paidAmount;

    /**
     * 支付状态（pending待支付/paid已支付/failed支付失败/refunded已退款）
     */
    private String status;

    /**
     * 支付方式（cash现金/wechat微信/alipay支付宝/bankcard银行卡等）
     */
    private String paymentMethod;

    /**
     * 支付时间
     */
    private Date paymentTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0存在 1删除）
     */
    @TableLogic
    private String delFlag;


}
