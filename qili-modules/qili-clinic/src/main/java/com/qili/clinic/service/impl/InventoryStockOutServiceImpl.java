package com.qili.clinic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qili.clinic.domain.bo.InventoryStockOutBo;
import com.qili.clinic.domain.InventoryStockOut;
import com.qili.clinic.domain.vo.InventoryStockOutVo;
import com.qili.clinic.mapper.InventoryStockOutMapper;
import com.qili.clinic.service.IInventoryStockOutService;
import com.qili.common.core.utils.MapstructUtils;
import com.qili.common.core.utils.StringUtils;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 库存出库主Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class InventoryStockOutServiceImpl implements IInventoryStockOutService {

    private final InventoryStockOutMapper baseMapper;

    /**
     * 查询库存出库主
     *
     * @param id 主键
     * @return 库存出库主
     */
    @Override
    public InventoryStockOutVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询库存出库主列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 库存出库主分页列表
     */
    @Override
    public TableDataInfo<InventoryStockOutVo> queryPageList(InventoryStockOutBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<InventoryStockOut> lqw = buildQueryWrapper(bo);
        Page<InventoryStockOutVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的库存出库主列表
     *
     * @param bo 查询条件
     * @return 库存出库主列表
     */
    @Override
    public List<InventoryStockOutVo> queryList(InventoryStockOutBo bo) {
        LambdaQueryWrapper<InventoryStockOut> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<InventoryStockOut> buildQueryWrapper(InventoryStockOutBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<InventoryStockOut> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(InventoryStockOut::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getStockOutNo()), InventoryStockOut::getStockOutNo, bo.getStockOutNo());
        lqw.eq(bo.getPharmacyId() != null, InventoryStockOut::getPharmacyId, bo.getPharmacyId());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), InventoryStockOut::getType, bo.getType());
        lqw.eq(bo.getRelatedId() != null, InventoryStockOut::getRelatedId, bo.getRelatedId());
        lqw.eq(bo.getTotalAmount() != null, InventoryStockOut::getTotalAmount, bo.getTotalAmount());
        lqw.eq(bo.getStockOutTime() != null, InventoryStockOut::getStockOutTime, bo.getStockOutTime());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), InventoryStockOut::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增库存出库主
     *
     * @param bo 库存出库主
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(InventoryStockOutBo bo) {
        InventoryStockOut add = MapstructUtils.convert(bo, InventoryStockOut.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改库存出库主
     *
     * @param bo 库存出库主
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(InventoryStockOutBo bo) {
        InventoryStockOut update = MapstructUtils.convert(bo, InventoryStockOut.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(InventoryStockOut entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除库存出库主信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
