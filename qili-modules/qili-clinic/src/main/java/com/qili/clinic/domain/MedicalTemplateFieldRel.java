package com.qili.clinic.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.qili.common.tenant.core.TenantEntity;
import lombok.*;

import java.io.Serial;

/**
 * 病历模板字段对象 qili_medical_template_field_rel
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("qili_medical_template_field_rel")
public class MedicalTemplateFieldRel extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 模板ID（关联qili_medical_template.id）
     */
    private Long templateId;

    /**
     * 字段ID（关联qili_medical_field.id）
     */
    private Long fieldId;

    /**
     * 字段显示顺序
     */
    private Long sortOrder;


}
