package com.qili.clinic.domain.bo;

import com.qili.clinic.domain.InventoryStockOut;
import com.qili.common.core.validate.AddGroup;
import com.qili.common.core.validate.EditGroup;
import com.qili.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 库存出库主业务对象 qili_inventory_stock_out
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = InventoryStockOut.class, reverseConvertGenerate = false)
public class InventoryStockOutBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 出库单号（自动生成如 CK202507240001）
     */
    private String stockOutNo;

    /**
     * 药房ID（qili_drug_pharmacy.id）
     */
    private Long pharmacyId;

    /**
     * 出库类型（prescription处方/manual手动/return退货）
     */
    @NotBlank(message = "出库类型（prescription处方/manual手动/return退货）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String type;

    /**
     * 关联ID（如处方ID）
     */
    @NotNull(message = "关联ID（如处方ID）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long relatedId;

    /**
     * 出库总金额（分）
     */
    @NotNull(message = "出库总金额（分）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long totalAmount;

    /**
     * 出库时间
     */
    @NotNull(message = "出库时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date stockOutTime;

    /**
     * 状态（pending待审核/approved已出库）
     */
    @NotBlank(message = "状态（pending待审核/approved已出库）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 备注
     */
    private String remark;


}
