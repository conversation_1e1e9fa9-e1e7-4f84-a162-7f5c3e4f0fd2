package com.qili.clinic.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.qili.clinic.domain.bo.InventoryStockInBo;
import com.qili.clinic.domain.vo.InventoryStockInVo;
import com.qili.clinic.service.IInventoryStockInService;
import com.qili.common.core.domain.R;
import com.qili.common.core.validate.AddGroup;
import com.qili.common.core.validate.EditGroup;
import com.qili.common.excel.utils.ExcelUtil;
import com.qili.common.idempotent.annotation.RepeatSubmit;
import com.qili.common.log.annotation.Log;
import com.qili.common.log.enums.BusinessType;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;
import com.qili.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 库存入库主
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/clinic/inventoryStockIn")
public class InventoryStockInController extends BaseController {

    private final IInventoryStockInService inventoryStockInService;

    /**
     * 查询库存入库主列表
     */
    @SaCheckPermission("clinic:inventoryStockIn:list")
    @GetMapping("/list")
    public TableDataInfo<InventoryStockInVo> list(InventoryStockInBo bo, PageQuery pageQuery) {
        return inventoryStockInService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出库存入库主列表
     */
    @SaCheckPermission("clinic:inventoryStockIn:export")
    @Log(title = "库存入库主", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(InventoryStockInBo bo, HttpServletResponse response) {
        List<InventoryStockInVo> list = inventoryStockInService.queryList(bo);
        ExcelUtil.exportExcel(list, "库存入库主", InventoryStockInVo.class, response);
    }

    /**
     * 获取库存入库主详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("clinic:inventoryStockIn:query")
    @GetMapping("/{id}")
    public R<InventoryStockInVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(inventoryStockInService.queryById(id));
    }

    /**
     * 新增库存入库主
     */
    @SaCheckPermission("clinic:inventoryStockIn:add")
    @Log(title = "库存入库主", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody InventoryStockInBo bo) {
        return toAjax(inventoryStockInService.insertByBo(bo));
    }

    /**
     * 修改库存入库主
     */
    @SaCheckPermission("clinic:inventoryStockIn:edit")
    @Log(title = "库存入库主", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody InventoryStockInBo bo) {
        return toAjax(inventoryStockInService.updateByBo(bo));
    }

    /**
     * 删除库存入库主
     *
     * @param ids 主键串
     */
    @SaCheckPermission("clinic:inventoryStockIn:remove")
    @Log(title = "库存入库主", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(inventoryStockInService.deleteWithValidByIds(List.of(ids), true));
    }
}
