package com.qili.clinic.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.qili.clinic.domain.Patient;
import com.qili.common.excel.annotation.ExcelDictFormat;
import com.qili.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 患者档案视图对象 qili_patients
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Patient.class)
public class PatientVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID，自增
     */
    @ExcelProperty(value = "主键ID，自增")
    private Long id;

    /**
     * 患者姓名
     */
    @ExcelProperty(value = "患者姓名")
    private String name;

    /**
     * 性别（0未知 1男 2女）
     */
    @ExcelProperty(value = "性别", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "qili_patients_gender")
    private String gender;

    /**
     * 年龄-年
     */
    @ExcelProperty(value = "年龄-年")
    private Long ageYear;

    /**
     * 年龄-月
     */
    @ExcelProperty(value = "年龄-月")
    private Long ageMonth;

    /**
     * 年龄-日
     */
    @ExcelProperty(value = "年龄-日")
    private Long ageDay;

    /**
     * 出生日期
     */
    @ExcelProperty(value = "出生日期")
    private Date dateOfBirth;

    /**
     * 手机号
     */
    @ExcelProperty(value = "手机号")
    private String phone;

    /**
     * 身份证号/证件号
     */
    @ExcelProperty(value = "身份证号/证件号")
    private String idCard;

    /**
     * 患者来源（如体检/门诊/网络等）
     */
    @ExcelProperty(value = "患者来源", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "如=体检/门诊/网络等")
    private String source;

    /**
     * 婚姻状况（未婚/已婚/离异/丧偶）
     */
    @ExcelProperty(value = "婚姻状况", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "未=婚/已婚/离异/丧偶")
    private String maritalStatus;

    /**
     * 体重（kg）
     */
    @ExcelProperty(value = "体重", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "k=g")
    private Long weight;

    /**
     * 省
     */
    @ExcelProperty(value = "省")
    private String addressProvince;

    /**
     * 市
     */
    @ExcelProperty(value = "市")
    private String addressCity;

    /**
     * 区/县
     */
    @ExcelProperty(value = "区/县")
    private String addressDistrict;

    /**
     * 详细地址
     */
    @ExcelProperty(value = "详细地址")
    private String addressDetail;

    /**
     * 职业
     */
    @ExcelProperty(value = "职业")
    private String occupation;

    /**
     * 档案号
     */
    @ExcelProperty(value = "档案号")
    private String code;

    /**
     * 工作单位
     */
    @ExcelProperty(value = "工作单位")
    private String employer;

    /**
     * 民族
     */
    @ExcelProperty(value = "民族")
    private String ethnicity;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 到店原因
     */
    @ExcelProperty(value = "到店原因")
    private String visitReason;

    /**
     * 既往史
     */
    @ExcelProperty(value = "既往史")
    private String pastHistory;

    /**
     * 过敏史
     */
    @ExcelProperty(value = "过敏史")
    private String allergyHistory;

    /**
     * 标签（多个标签用逗号分隔）
     */
    @ExcelProperty(value = "标签", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "多=个标签用逗号分隔")
    private String tags;

    /**
     * 就诊状态
     */
    @ExcelProperty(value = "就诊状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "qili_medical_record_status")
    private MedicalRecordVo medicalRecord;

}
