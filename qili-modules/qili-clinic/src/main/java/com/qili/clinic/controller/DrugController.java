package com.qili.clinic.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.qili.clinic.domain.bo.DrugBo;
import com.qili.clinic.domain.vo.DrugVo;
import com.qili.clinic.service.IDrugService;
import com.qili.common.core.domain.R;
import com.qili.common.core.validate.AddGroup;
import com.qili.common.core.validate.EditGroup;
import com.qili.common.excel.utils.ExcelUtil;
import com.qili.common.idempotent.annotation.RepeatSubmit;
import com.qili.common.log.annotation.Log;
import com.qili.common.log.enums.BusinessType;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;
import com.qili.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 药品信息
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/clinic/drug")
public class DrugController extends BaseController {

    private final IDrugService drugService;

    /**
     * 查询药品信息列表
     */
    @SaCheckPermission("clinic:drug:list")
    @GetMapping("/list")
    public TableDataInfo<DrugVo> list(DrugBo bo, PageQuery pageQuery) {
        return drugService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出药品信息列表
     */
    @SaCheckPermission("clinic:drug:export")
    @Log(title = "药品信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(DrugBo bo, HttpServletResponse response) {
        List<DrugVo> list = drugService.queryList(bo);
        ExcelUtil.exportExcel(list, "药品信息", DrugVo.class, response);
    }

    /**
     * 获取药品信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("clinic:drug:query")
    @GetMapping("/{id}")
    public R<DrugVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(drugService.queryById(id));
    }

    /**
     * 新增药品信息
     */
    @SaCheckPermission("clinic:drug:add")
    @Log(title = "药品信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody DrugBo bo) {
        return toAjax(drugService.insertByBo(bo));
    }

    /**
     * 修改药品信息
     */
    @SaCheckPermission("clinic:drug:edit")
    @Log(title = "药品信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody DrugBo bo) {
        return toAjax(drugService.updateByBo(bo));
    }

    /**
     * 删除药品信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("clinic:drug:remove")
    @Log(title = "药品信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(drugService.deleteWithValidByIds(List.of(ids), true));
    }
}
