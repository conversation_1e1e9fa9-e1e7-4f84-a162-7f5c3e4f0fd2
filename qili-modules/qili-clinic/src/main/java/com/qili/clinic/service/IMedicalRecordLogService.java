package com.qili.clinic.service;

import com.qili.clinic.domain.vo.MedicalRecordLogVo;
import com.qili.clinic.domain.bo.MedicalRecordLogBo;
import com.qili.common.mybatis.core.page.TableDataInfo;
import com.qili.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 病历状态变更日志Service接口
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
public interface IMedicalRecordLogService {

    /**
     * 查询病历状态变更日志
     *
     * @param id 主键
     * @return 病历状态变更日志
     */
    MedicalRecordLogVo queryById(Long id);

    /**
     * 分页查询病历状态变更日志列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 病历状态变更日志分页列表
     */
    TableDataInfo<MedicalRecordLogVo> queryPageList(MedicalRecordLogBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的病历状态变更日志列表
     *
     * @param bo 查询条件
     * @return 病历状态变更日志列表
     */
    List<MedicalRecordLogVo> queryList(MedicalRecordLogBo bo);

    /**
     * 新增病历状态变更日志
     *
     * @param bo 病历状态变更日志
     * @return 是否新增成功
     */
    Boolean insertByBo(MedicalRecordLogBo bo);

    /**
     * 异步新增病历状态变更日志
     * @param bo 病历状态变更日志
     */
    void insertAsync(MedicalRecordLogBo bo);

    /**
     * 修改病历状态变更日志
     *
     * @param bo 病历状态变更日志
     * @return 是否修改成功
     */
    Boolean updateByBo(MedicalRecordLogBo bo);

    /**
     * 校验并批量删除病历状态变更日志信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据病历ID查询状态变更日志列表
     * @param id 病历ID
     * @return 状态变更日志列表
     */
    List<MedicalRecordLogVo> queryListByRecordId(Long id);
}
