package com.qili.clinic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qili.clinic.domain.bo.InventoryStockOutItemBo;
import com.qili.clinic.domain.InventoryStockOutItem;
import com.qili.clinic.domain.vo.InventoryStockOutItemVo;
import com.qili.clinic.mapper.InventoryStockOutItemMapper;
import com.qili.clinic.service.IInventoryStockOutItemService;
import com.qili.common.core.utils.MapstructUtils;
import com.qili.common.core.utils.StringUtils;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 库存出库子Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class InventoryStockOutItemServiceImpl implements IInventoryStockOutItemService {

    private final InventoryStockOutItemMapper baseMapper;

    /**
     * 查询库存出库子
     *
     * @param id 主键
     * @return 库存出库子
     */
    @Override
    public InventoryStockOutItemVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询库存出库子列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 库存出库子分页列表
     */
    @Override
    public TableDataInfo<InventoryStockOutItemVo> queryPageList(InventoryStockOutItemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<InventoryStockOutItem> lqw = buildQueryWrapper(bo);
        Page<InventoryStockOutItemVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的库存出库子列表
     *
     * @param bo 查询条件
     * @return 库存出库子列表
     */
    @Override
    public List<InventoryStockOutItemVo> queryList(InventoryStockOutItemBo bo) {
        LambdaQueryWrapper<InventoryStockOutItem> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<InventoryStockOutItem> buildQueryWrapper(InventoryStockOutItemBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<InventoryStockOutItem> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(InventoryStockOutItem::getId);
        lqw.eq(bo.getStockOutId() != null, InventoryStockOutItem::getStockOutId, bo.getStockOutId());
        lqw.eq(bo.getDrugId() != null, InventoryStockOutItem::getDrugId, bo.getDrugId());
        lqw.eq(StringUtils.isNotBlank(bo.getBatchNo()), InventoryStockOutItem::getBatchNo, bo.getBatchNo());
        lqw.eq(bo.getSellPrice() != null, InventoryStockOutItem::getSellPrice, bo.getSellPrice());
        lqw.eq(bo.getQuantity() != null, InventoryStockOutItem::getQuantity, bo.getQuantity());
        lqw.eq(bo.getAmount() != null, InventoryStockOutItem::getAmount, bo.getAmount());
        return lqw;
    }

    /**
     * 新增库存出库子
     *
     * @param bo 库存出库子
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(InventoryStockOutItemBo bo) {
        InventoryStockOutItem add = MapstructUtils.convert(bo, InventoryStockOutItem.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改库存出库子
     *
     * @param bo 库存出库子
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(InventoryStockOutItemBo bo) {
        InventoryStockOutItem update = MapstructUtils.convert(bo, InventoryStockOutItem.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(InventoryStockOutItem entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除库存出库子信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
