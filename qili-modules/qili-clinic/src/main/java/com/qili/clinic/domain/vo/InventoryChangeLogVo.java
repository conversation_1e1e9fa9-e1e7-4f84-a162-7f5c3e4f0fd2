package com.qili.clinic.domain.vo;

import java.util.Date;

import com.qili.clinic.domain.InventoryChangeLog;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.qili.common.excel.annotation.ExcelDictFormat;
import com.qili.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 药品库存变更记录视图对象 qili_inventory_change_log
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = InventoryChangeLog.class)
public class InventoryChangeLogVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 药房ID
     */
    @ExcelProperty(value = "药房ID")
    private Long pharmacyId;

    /**
     * 药品ID
     */
    @ExcelProperty(value = "药品ID")
    private Long drugId;

    /**
     * 药品名称
     */
    @ExcelProperty(value = "药品名称")
    private String drugName;

    /**
     * 批次号
     */
    @ExcelProperty(value = "批次号")
    private String batchNo;

    /**
     * 单位（如 g/片/盒 等）
     */
    @ExcelProperty(value = "单位", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "如=,g=/片/盒,等=")
    private String unit;

    /**
     * 变更类型（1入库 2出库 3盘盈 4盘亏 5退货 6调拨）
     */
    @ExcelProperty(value = "变更类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=入库,2=出库,3=盘盈,4=盘亏,5=退货,6=调拨")
    private Long changeType;

    /**
     * 变更数量（正增负减）
     */
    @ExcelProperty(value = "变更数量", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "正=增负减")
    private Long changeQty;

    /**
     * 变更前数量
     */
    @ExcelProperty(value = "变更前数量")
    private Long beforeQty;

    /**
     * 变更后数量
     */
    @ExcelProperty(value = "变更后数量")
    private Long afterQty;

    /**
     * 来源类型（stock_in/stock_out/stocktaking/…）
     */
    @ExcelProperty(value = "来源类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "s=tock_in/stock_out/stocktaking/…")
    private String sourceType;

    /**
     * 来源单号或子表ID
     */
    @ExcelProperty(value = "来源单号或子表ID")
    private String sourceNo;

    /**
     * 变更时间
     */
    @ExcelProperty(value = "变更时间")
    private Date changeTime;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
