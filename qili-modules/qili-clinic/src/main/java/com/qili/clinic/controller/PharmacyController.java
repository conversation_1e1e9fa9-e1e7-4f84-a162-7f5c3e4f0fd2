package com.qili.clinic.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.qili.common.idempotent.annotation.RepeatSubmit;
import com.qili.common.log.annotation.Log;
import com.qili.common.web.core.BaseController;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.core.domain.R;
import com.qili.common.core.validate.AddGroup;
import com.qili.common.core.validate.EditGroup;
import com.qili.common.log.enums.BusinessType;
import com.qili.common.excel.utils.ExcelUtil;
import com.qili.clinic.domain.vo.PharmacyVo;
import com.qili.clinic.domain.bo.PharmacyBo;
import com.qili.clinic.service.IPharmacyService;
import com.qili.common.mybatis.core.page.TableDataInfo;

/**
 * 药房信息
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/clinic/pharmacy")
public class PharmacyController extends BaseController {

    private final IPharmacyService pharmacyService;

    /**
     * 查询药房信息列表
     */
    @SaCheckPermission("clinic:pharmacy:list")
    @GetMapping("/list")
    public TableDataInfo<PharmacyVo> list(PharmacyBo bo, PageQuery pageQuery) {
        return pharmacyService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出药房信息列表
     */
    @SaCheckPermission("clinic:pharmacy:export")
    @Log(title = "药房信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PharmacyBo bo, HttpServletResponse response) {
        List<PharmacyVo> list = pharmacyService.queryList(bo);
        ExcelUtil.exportExcel(list, "药房信息", PharmacyVo.class, response);
    }

    /**
     * 获取药房信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("clinic:pharmacy:query")
    @GetMapping("/{id}")
    public R<PharmacyVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(pharmacyService.queryById(id));
    }

    /**
     * 新增药房信息
     */
    @SaCheckPermission("clinic:pharmacy:add")
    @Log(title = "药房信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PharmacyBo bo) {
        return toAjax(pharmacyService.insertByBo(bo));
    }

    /**
     * 修改药房信息
     */
    @SaCheckPermission("clinic:pharmacy:edit")
    @Log(title = "药房信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PharmacyBo bo) {
        return toAjax(pharmacyService.updateByBo(bo));
    }

    /**
     * 删除药房信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("clinic:pharmacy:remove")
    @Log(title = "药房信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(pharmacyService.deleteWithValidByIds(List.of(ids), true));
    }
}
