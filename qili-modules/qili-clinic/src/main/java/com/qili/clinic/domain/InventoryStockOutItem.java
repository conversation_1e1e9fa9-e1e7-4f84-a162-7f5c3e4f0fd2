package com.qili.clinic.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.qili.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 库存出库子对象 qili_inventory_stock_out_item
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("qili_inventory_stock_out_item")
public class InventoryStockOutItem extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 出库单ID（关联qili_inventory_stock_out.id）
     */
    private Long stockOutId;

    /**
     * 药品ID（qili_drug.id）
     */
    private Long drugId;

    /**
     * 出库批次号（可选）
     */
    private String batchNo;

    /**
     * 出库价格（分）
     */
    private Long sellPrice;

    /**
     * 出库数量
     */
    private Long quantity;

    /**
     * 小计金额（分）
     */
    private Long amount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0存在 1删除）
     */
    @TableLogic
    private String delFlag;


}
