package com.qili.clinic.service;

import com.qili.clinic.domain.vo.PharmacyVo;
import com.qili.clinic.domain.bo.PharmacyBo;
import com.qili.common.mybatis.core.page.TableDataInfo;
import com.qili.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 药房信息Service接口
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
public interface IPharmacyService {

    /**
     * 查询药房信息
     *
     * @param id 主键
     * @return 药房信息
     */
    PharmacyVo queryById(Long id);

    /**
     * 分页查询药房信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 药房信息分页列表
     */
    TableDataInfo<PharmacyVo> queryPageList(PharmacyBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的药房信息列表
     *
     * @param bo 查询条件
     * @return 药房信息列表
     */
    List<PharmacyVo> queryList(PharmacyBo bo);

    /**
     * 新增药房信息
     *
     * @param bo 药房信息
     * @return 是否新增成功
     */
    Boolean insertByBo(PharmacyBo bo);

    /**
     * 修改药房信息
     *
     * @param bo 药房信息
     * @return 是否修改成功
     */
    Boolean updateByBo(PharmacyBo bo);

    /**
     * 校验并批量删除药房信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
