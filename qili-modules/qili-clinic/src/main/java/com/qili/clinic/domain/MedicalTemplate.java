package com.qili.clinic.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.qili.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 病历模板对象 qili_medical_template
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("qili_medical_template")
public class MedicalTemplate extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 模板名称（如：内科通用模板）
     */
    private String name;

    /**
     * 模板类型（中医/西医/中西医结合）
     */
    private String type;

    /**
     * 是否默认模板（0：否，1：是）
     */
    private String defaultFlag;

    /**
     * 所属目录
     */
    private String directory;

    /**
     * 模板描述
     */
    private String description;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;

}
