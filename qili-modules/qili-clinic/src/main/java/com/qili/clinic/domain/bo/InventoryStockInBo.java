package com.qili.clinic.domain.bo;

import com.qili.clinic.domain.InventoryStockIn;
import com.qili.common.core.validate.AddGroup;
import com.qili.common.core.validate.EditGroup;
import com.qili.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 库存入库主业务对象 qili_inventory_stock_in
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = InventoryStockIn.class, reverseConvertGenerate = false)
public class InventoryStockInBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 入库单号（自动生成如 RK202507240001）
     */
    private String stockInNo;

    /**
     * 药房ID（qili_drug_pharmacy.id）
     */
    private Long pharmacyId;

    /**
     * 供应商
     */
    private String supplier;

    /**
     * 入库时间
     */
    private String stockInTime;

    /**
     * 备注
     */
    private String remark;


    /**
     * 入库明细
     */
    @NotEmpty(message = "入库明细不能为空，请添加药品信息",groups = { AddGroup.class, EditGroup.class })
    @NotNull(message = "入库明细不能为空，请添加药品信息",groups = { AddGroup.class, EditGroup.class })
    private List<InventoryStockInItemBo> stockInItemList;

}
