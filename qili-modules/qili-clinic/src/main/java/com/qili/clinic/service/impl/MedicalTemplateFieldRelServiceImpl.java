package com.qili.clinic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qili.clinic.domain.bo.MedicalTemplateFieldRelBo;
import com.qili.clinic.domain.MedicalTemplateFieldRel;
import com.qili.clinic.domain.vo.MedicalTemplateFieldRelVo;
import com.qili.clinic.mapper.MedicalTemplateFieldRelMapper;
import com.qili.clinic.service.IMedicalTemplateFieldRelService;
import com.qili.common.core.utils.MapstructUtils;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 病历模板字段Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MedicalTemplateFieldRelServiceImpl implements IMedicalTemplateFieldRelService {

    private final MedicalTemplateFieldRelMapper baseMapper;

    /**
     * 查询病历模板字段
     *
     * @param id 主键
     * @return 病历模板字段
     */
    @Override
    public MedicalTemplateFieldRelVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询病历模板字段列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 病历模板字段分页列表
     */
    @Override
    public TableDataInfo<MedicalTemplateFieldRelVo> queryPageList(MedicalTemplateFieldRelBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MedicalTemplateFieldRel> lqw = buildQueryWrapper(bo);
        Page<MedicalTemplateFieldRelVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的病历模板字段列表
     *
     * @param bo 查询条件
     * @return 病历模板字段列表
     */
    @Override
    public List<MedicalTemplateFieldRelVo> queryList(MedicalTemplateFieldRelBo bo) {
        LambdaQueryWrapper<MedicalTemplateFieldRel> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MedicalTemplateFieldRel> buildQueryWrapper(MedicalTemplateFieldRelBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MedicalTemplateFieldRel> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(MedicalTemplateFieldRel::getId);
        lqw.eq(bo.getTemplateId() != null, MedicalTemplateFieldRel::getTemplateId, bo.getTemplateId());
        lqw.eq(bo.getFieldId() != null, MedicalTemplateFieldRel::getFieldId, bo.getFieldId());
        return lqw;
    }

    /**
     * 新增病历模板字段
     *
     * @param bo 病历模板字段
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MedicalTemplateFieldRelBo bo) {
        MedicalTemplateFieldRel add = MapstructUtils.convert(bo, MedicalTemplateFieldRel.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改病历模板字段
     *
     * @param bo 病历模板字段
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MedicalTemplateFieldRelBo bo) {
        MedicalTemplateFieldRel update = MapstructUtils.convert(bo, MedicalTemplateFieldRel.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MedicalTemplateFieldRel entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除病历模板字段信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public List<MedicalTemplateFieldRelVo> listByTemplateIds(Collection<Long> templateIds) {
        LambdaQueryWrapper<MedicalTemplateFieldRel> lqw = Wrappers.lambdaQuery();
        lqw.in(MedicalTemplateFieldRel::getTemplateId, templateIds);
        return baseMapper.selectVoList(lqw);
    }
}
