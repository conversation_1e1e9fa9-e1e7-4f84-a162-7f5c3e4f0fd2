package com.qili.clinic.service.impl;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.util.NumberUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qili.clinic.domain.bo.PaymentBo;
import com.qili.clinic.domain.MedicalRecord;
import com.qili.clinic.domain.Payment;
import com.qili.clinic.domain.PaymentDetail;
import com.qili.clinic.domain.vo.PaymentVo;
import com.qili.clinic.enums.PaymentOrderType;
import com.qili.clinic.enums.PaymentStatus;
import com.qili.clinic.mapper.PaymentDetailMapper;
import com.qili.clinic.mapper.PaymentMapper;
import com.qili.clinic.service.IPaymentService;
import com.qili.common.core.exception.ServiceException;
import com.qili.common.core.utils.MapstructUtils;
import com.qili.common.core.utils.StringUtils;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 支付主Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PaymentServiceImpl implements IPaymentService {

    private final PaymentMapper baseMapper;
    private final PaymentDetailMapper paymentDetailMapper;

    /**
     * 查询支付主
     *
     * @param id 主键
     * @return 支付主
     */
    @Override
    public PaymentVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询支付主列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 支付主分页列表
     */
    @Override
    public TableDataInfo<PaymentVo> queryPageList(PaymentBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Payment> lqw = buildQueryWrapper(bo);
        Page<PaymentVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的支付主列表
     *
     * @param bo 查询条件
     * @return 支付主列表
     */
    @Override
    public List<PaymentVo> queryList(PaymentBo bo) {
        LambdaQueryWrapper<Payment> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Payment> buildQueryWrapper(PaymentBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Payment> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(Payment::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getPaymentNo()), Payment::getPaymentNo, bo.getPaymentNo());
        lqw.eq(bo.getPatientId() != null, Payment::getPatientId, bo.getPatientId());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderType()), Payment::getOrderType, bo.getOrderType());
        lqw.eq(StringUtils.isNotBlank(bo.getPaymentMethod()), Payment::getPaymentMethod, bo.getPaymentMethod());
        lqw.like(bo.getPaymentTime() != null, Payment::getPaymentTime, bo.getPaymentTime());
        return lqw;
    }

    /**
     * 新增支付主
     *
     * @param bo 支付主
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(PaymentBo bo) {
        Payment add = MapstructUtils.convert(bo, Payment.class);
        Long paidAmountLong = NumberUtil.mul(bo.getPaidAmount(), 100).longValue();
        add.setPaidAmount(paidAmountLong);
        Long totalAmountLong = NumberUtil.mul(bo.getTotalAmount(), 100).longValue();
        add.setTotalAmount(totalAmountLong);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Payment entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除支付主信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    @Transactional(rollbackFor = ServiceException.class)
    public void savePayment(MedicalRecord medicalRecord, PaymentBo payment) {
        if (ObjectUtil.isNull(payment)) {
            return;
        }
        payment.setPatientId(medicalRecord.getPatientId());
        payment.setPatientName(medicalRecord.getPatientName());
        payment.setSourceId(medicalRecord.getId());
        payment.setOrderType(PaymentOrderType.PRESCRIPTION.getCode());
        payment.setStatus(PaymentStatus.PAID.getCode());
        payment.setPaymentTime(new Date());
        payment.setPaymentNo("ZF" + IdUtil.getSnowflakeNextIdStr());
        this.insertByBo(payment);
        this.savePaymentDetail(payment);
    }

    /**
     * 保存支付项
     *
     * @param bo 支付
     */
    private void savePaymentDetail(PaymentBo bo) {
        List<PaymentDetail> paymentDetailList = bo.getPaymentDetailList().stream()
                .map(paymentDetail -> {
                    PaymentDetail add = MapstructUtils.convert(paymentDetail, PaymentDetail.class);
                    add.setPaymentId(bo.getId());
                    return add;
                }).collect(Collectors.toList());
        paymentDetailMapper.insertBatch(paymentDetailList);
    }
}
