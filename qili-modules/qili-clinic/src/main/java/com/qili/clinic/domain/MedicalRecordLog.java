package com.qili.clinic.domain;

import com.qili.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

import java.io.Serial;

/**
 * 病历状态变更日志对象 qili_medical_record_log
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("qili_medical_record_log")
public class MedicalRecordLog extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 病历ID（关联qili_medical_record.id）
     */
    private Long recordId;

    /**
     * 操作状态（WAITING=待接诊, IN_PROGRESS=就诊中, PRESCRIPTION_DONE=处方已开, DISPENSING=取药中, DECOCTING=煎药中, COMPLETED=已完成, CANCELLED=已取消）
     */
    private String action;

    /**
     * 操作名称（冗余中文，如 待接诊、就诊中）
     */
    private String actionName;

    /**
     * 操作时间
     */
    private Date actionTime;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;


}
