package com.qili.clinic.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.qili.common.tenant.core.TenantEntity;
import lombok.*;

import java.io.Serial;

/**
 * 病历字段值对象 qili_medical_field_data
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("qili_medical_field_data")
public class MedicalFieldData extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 分组顺序
     */
    private int groupSortOrder;

    /**
     * 字段ID（关联qili_medical_field.id）
     */
    private Long fieldId;

    /**
     * 字段值（如 咳嗽）
     */
    private String data;

    /**
     * 显示顺序
     */
    private Long sortOrder;

    /**
     * 是否系统默认值（1是 0否）
     */
    private String defaultFlag;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;

}
