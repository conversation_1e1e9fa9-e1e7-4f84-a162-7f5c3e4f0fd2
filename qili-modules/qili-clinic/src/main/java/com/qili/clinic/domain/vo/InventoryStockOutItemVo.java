package com.qili.clinic.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.qili.clinic.domain.InventoryStockOutItem;
import com.qili.common.excel.annotation.ExcelDictFormat;
import com.qili.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 库存出库子视图对象 qili_inventory_stock_out_item
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = InventoryStockOutItem.class)
public class InventoryStockOutItemVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 出库单ID（关联qili_inventory_stock_out.id）
     */
    @ExcelProperty(value = "出库单ID", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "关=联qili_inventory_stock_out.id")
    private Long stockOutId;

    /**
     * 药品ID（qili_drug.id）
     */
    @ExcelProperty(value = "药品ID", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "q=ili_drug.id")
    private Long drugId;

    /**
     * 出库批次号（可选）
     */
    @ExcelProperty(value = "出库批次号", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "可=选")
    private String batchNo;

    /**
     * 出库价格（分）
     */
    @ExcelProperty(value = "出库价格", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "分=")
    private Long sellPrice;

    /**
     * 出库数量
     */
    @ExcelProperty(value = "出库数量")
    private Long quantity;

    /**
     * 小计金额（分）
     */
    @ExcelProperty(value = "小计金额", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "分=")
    private Long amount;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

}
