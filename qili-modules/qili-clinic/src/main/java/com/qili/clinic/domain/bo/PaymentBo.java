package com.qili.clinic.domain.bo;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.qili.clinic.domain.Payment;
import com.qili.common.core.validate.AddGroup;
import com.qili.common.core.validate.EditGroup;
import com.qili.common.mybatis.core.domain.BaseEntity;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 支付主业务对象 qili_payment
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Payment.class, reverseConvertGenerate = false)
public class PaymentBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 支付单号（如 ZF202508140001）
     */
    private String paymentNo;

    /**
     * 患者ID（关联患者表）
     */
    private Long patientId;

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 业务类型（registration挂号/prescription处方/treatment理疗等）
     */
    private String orderType;

    /**
     * 业务ID
     */
    private Long sourceId;

    /**
     * 应付总金额（分）
     */
    private BigDecimal totalAmount;

    /**
     * 实付金额（分）
     */
    private BigDecimal paidAmount;

    /**
     * 支付状态（pending待支付/paid已支付/failed支付失败/refunded已退款）
     */
    private String status;

    /**
     * 支付方式（cash现金/wechat微信/alipay支付宝/bankcard银行卡等）
     */
    @NotBlank(message = "支付方式（cash现金/wechat微信/alipay支付宝/bankcard银行卡等）不能为空", groups = { AddGroup.class,
            EditGroup.class })
    private String paymentMethod;

    /**
     * 支付时间
     */
    @NotNull(message = "支付时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date paymentTime;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;

    /**
     * 支付项
     */
    private List<PaymentDetailBo> paymentDetailList;
}
