package com.qili.clinic.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.qili.clinic.domain.bo.InventoryStockOutItemBo;
import com.qili.clinic.domain.vo.InventoryStockOutItemVo;
import com.qili.clinic.service.IInventoryStockOutItemService;
import com.qili.common.core.domain.R;
import com.qili.common.core.validate.AddGroup;
import com.qili.common.core.validate.EditGroup;
import com.qili.common.excel.utils.ExcelUtil;
import com.qili.common.idempotent.annotation.RepeatSubmit;
import com.qili.common.log.annotation.Log;
import com.qili.common.log.enums.BusinessType;
import com.qili.common.mybatis.core.page.PageQuery;
import com.qili.common.mybatis.core.page.TableDataInfo;
import com.qili.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 库存出库子
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/clinic/inventoryStockOutItem")
public class InventoryStockOutItemController extends BaseController {

    private final IInventoryStockOutItemService inventoryStockOutItemService;

    /**
     * 查询库存出库子列表
     */
    @SaCheckPermission("clinic:inventoryStockOutItem:list")
    @GetMapping("/list")
    public TableDataInfo<InventoryStockOutItemVo> list(InventoryStockOutItemBo bo, PageQuery pageQuery) {
        return inventoryStockOutItemService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出库存出库子列表
     */
    @SaCheckPermission("clinic:inventoryStockOutItem:export")
    @Log(title = "库存出库子", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(InventoryStockOutItemBo bo, HttpServletResponse response) {
        List<InventoryStockOutItemVo> list = inventoryStockOutItemService.queryList(bo);
        ExcelUtil.exportExcel(list, "库存出库子", InventoryStockOutItemVo.class, response);
    }

    /**
     * 获取库存出库子详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("clinic:inventoryStockOutItem:query")
    @GetMapping("/{id}")
    public R<InventoryStockOutItemVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(inventoryStockOutItemService.queryById(id));
    }

    /**
     * 新增库存出库子
     */
    @SaCheckPermission("clinic:inventoryStockOutItem:add")
    @Log(title = "库存出库子", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody InventoryStockOutItemBo bo) {
        return toAjax(inventoryStockOutItemService.insertByBo(bo));
    }

    /**
     * 修改库存出库子
     */
    @SaCheckPermission("clinic:inventoryStockOutItem:edit")
    @Log(title = "库存出库子", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody InventoryStockOutItemBo bo) {
        return toAjax(inventoryStockOutItemService.updateByBo(bo));
    }

    /**
     * 删除库存出库子
     *
     * @param ids 主键串
     */
    @SaCheckPermission("clinic:inventoryStockOutItem:remove")
    @Log(title = "库存出库子", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(inventoryStockOutItemService.deleteWithValidByIds(List.of(ids), true));
    }
}
