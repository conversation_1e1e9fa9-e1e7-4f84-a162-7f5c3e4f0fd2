<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.qili</groupId>
    <artifactId>qili-common-bom</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <description>
        qili-common-bom common依赖项
    </description>

    <properties>
        <revision>5.4.1</revision>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- 核心模块 -->
            <dependency>
                <groupId>com.qili</groupId>
                <artifactId>qili-common-core</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 接口模块 -->
            <dependency>
                <groupId>com.qili</groupId>
                <artifactId>qili-common-doc</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- excel -->
            <dependency>
                <groupId>com.qili</groupId>
                <artifactId>qili-common-excel</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 幂等 -->
            <dependency>
                <groupId>com.qili</groupId>
                <artifactId>qili-common-idempotent</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 调度模块 -->
            <dependency>
                <groupId>com.qili</groupId>
                <artifactId>qili-common-job</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 日志记录 -->
            <dependency>
                <groupId>com.qili</groupId>
                <artifactId>qili-common-log</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 邮件服务 -->
            <dependency>
                <groupId>com.qili</groupId>
                <artifactId>qili-common-mail</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 数据库服务 -->
            <dependency>
                <groupId>com.qili</groupId>
                <artifactId>qili-common-mybatis</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- OSS -->
            <dependency>
                <groupId>com.qili</groupId>
                <artifactId>qili-common-oss</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 限流 -->
            <dependency>
                <groupId>com.qili</groupId>
                <artifactId>qili-common-ratelimiter</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 缓存服务 -->
            <dependency>
                <groupId>com.qili</groupId>
                <artifactId>qili-common-redis</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- satoken -->
            <dependency>
                <groupId>com.qili</groupId>
                <artifactId>qili-common-satoken</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 安全模块 -->
            <dependency>
                <groupId>com.qili</groupId>
                <artifactId>qili-common-security</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 短信模块 -->
            <dependency>
                <groupId>com.qili</groupId>
                <artifactId>qili-common-sms</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.qili</groupId>
                <artifactId>qili-common-social</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- web服务 -->
            <dependency>
                <groupId>com.qili</groupId>
                <artifactId>qili-common-web</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 翻译模块 -->
            <dependency>
                <groupId>com.qili</groupId>
                <artifactId>qili-common-translation</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 脱敏模块 -->
            <dependency>
                <groupId>com.qili</groupId>
                <artifactId>qili-common-sensitive</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 序列化模块 -->
            <dependency>
                <groupId>com.qili</groupId>
                <artifactId>qili-common-json</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 数据库加解密模块 -->
            <dependency>
                <groupId>com.qili</groupId>
                <artifactId>qili-common-encrypt</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 租户模块 -->
            <dependency>
                <groupId>com.qili</groupId>
                <artifactId>qili-common-tenant</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- WebSocket模块 -->
            <dependency>
                <groupId>com.qili</groupId>
                <artifactId>qili-common-websocket</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- SSE模块 -->
            <dependency>
                <groupId>com.qili</groupId>
                <artifactId>qili-common-sse</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.qili</groupId>
                <artifactId>qili-clinic</artifactId>
                <version>${revision}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>
