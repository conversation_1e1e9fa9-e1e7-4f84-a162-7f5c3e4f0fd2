<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>qili-cis</artifactId>
        <groupId>com.qili</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <modules>
        <module>qili-common-bom</module>
        <module>qili-common-social</module>
        <module>qili-common-core</module>
        <module>qili-common-doc</module>
        <module>qili-common-excel</module>
        <module>qili-common-idempotent</module>
        <module>qili-common-job</module>
        <module>qili-common-log</module>
        <module>qili-common-mail</module>
        <module>qili-common-mybatis</module>
        <module>qili-common-oss</module>
        <module>qili-common-ratelimiter</module>
        <module>qili-common-redis</module>
        <module>qili-common-satoken</module>
        <module>qili-common-security</module>
        <module>qili-common-sms</module>
        <module>qili-common-web</module>
        <module>qili-common-translation</module>
        <module>qili-common-sensitive</module>
        <module>qili-common-json</module>
        <module>qili-common-encrypt</module>
        <module>qili-common-tenant</module>
        <module>qili-common-websocket</module>
        <module>qili-common-sse</module>
    </modules>

    <artifactId>qili-common</artifactId>
    <packaging>pom</packaging>

    <description>
        common 通用模块
    </description>

</project>
