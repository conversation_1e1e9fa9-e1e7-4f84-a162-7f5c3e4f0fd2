package com.qili;

import cn.hutool.core.date.DateUtil;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;

/**
 * 启动程序
 *
 * <AUTHOR> Li
 */

@SpringBootApplication
@MapperScan("com.qili.**.mapper")
public class QiliApplication {

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(QiliApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        String date = DateUtil.now(); // 自动取当天日期
        String banner = String.format("""
                                          \033[1;36m
                                          ╔══════════════════════════════════════╗
                                          ║   🚀  Application qili-cis 启动成功！  ║
                                          ║   📅  日期: %s       ║
                                          ╚══════════════════════════════════════╝
                                          \033[0m
                                          """, date);
        System.out.println(banner);
    }

}
